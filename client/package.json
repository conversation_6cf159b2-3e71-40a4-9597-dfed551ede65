{"name": "black-myth-wukong-client", "version": "1.0.0", "description": "《黑神话：悟空》妖怪平生录前端", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"vue": "^2.6.14", "vue-router": "^3.5.4", "vuex": "^3.6.2", "element-ui": "^2.15.13", "axios": "^1.4.0", "lodash": "^4.17.21"}, "devDependencies": {"@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-standard": "^6.1.0", "eslint": "^7.32.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.2.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.62.1", "sass-loader": "^13.2.2", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}