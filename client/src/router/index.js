import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: {
      title: '寻找天命人 - 黑神话悟空妖怪平生录',
      description: '203个妖怪，203首小诗，203个妖生故事'
    }
  },
  {
    path: '/monsters',
    name: 'MonsterList',
    component: () => import('@/views/MonsterList.vue'),
    meta: {
      title: '妖怪图鉴 - 寻找天命人',
      description: '浏览所有妖怪的平生录'
    }
  },
  {
    path: '/monster/:id',
    name: 'MonsterDetail',
    component: () => import('@/views/MonsterDetail.vue'),
    meta: {
      title: '妖怪详情 - 寻找天命人'
    }
  },
  {
    path: '/chapter/:code',
    name: 'ChapterDetail',
    component: () => import('@/views/ChapterDetail.vue'),
    meta: {
      title: '章节详情 - 寻找天命人'
    }
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/views/Search.vue'),
    meta: {
      title: '搜索妖怪 - 寻找天命人'
    }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('@/views/About.vue'),
    meta: {
      title: '关于项目 - 寻找天命人',
      description: '了解黑神话悟空妖怪平生录项目'
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到 - 寻找天命人'
    }
  },
  {
    path: '*',
    redirect: '/404'
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }
  
  // 设置页面描述
  if (to.meta.description) {
    const metaDescription = document.querySelector('meta[name="description"]')
    if (metaDescription) {
      metaDescription.setAttribute('content', to.meta.description)
    }
  }
  
  next()
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
})

export default router
