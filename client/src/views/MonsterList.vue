<template>
  <div class="monster-list">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">妖怪图鉴</h1>
        <p class="page-subtitle">探索《黑神话：悟空》中的妖怪世界</p>
      </div>

      <!-- 筛选和搜索 -->
      <div class="filter-section">
        <div class="filter-row">
          <!-- 章节筛选 -->
          <div class="filter-item">
            <label>章节筛选：</label>
            <el-select v-model="filters.chapter" placeholder="选择章节" clearable @change="handleFilterChange">
              <el-option label="全部章节" value=""></el-option>
              <el-option 
                v-for="chapter in chapters" 
                :key="chapter.code"
                :label="chapter.name" 
                :value="chapter.code"
              ></el-option>
            </el-select>
          </div>

          <!-- 搜索框 -->
          <div class="filter-item">
            <label>搜索：</label>
            <el-input
              v-model="filters.name"
              placeholder="输入妖怪名称..."
              @keyup.enter.native="handleFilterChange"
              clearable
            >
              <el-button slot="append" icon="el-icon-search" @click="handleFilterChange"></el-button>
            </el-input>
          </div>

          <!-- 排序方式 -->
          <div class="filter-item">
            <label>排序：</label>
            <el-select v-model="filters.sortBy" @change="handleFilterChange">
              <el-option label="章节顺序" value="chapter_num,monster_num"></el-option>
              <el-option label="名称排序" value="name"></el-option>
            </el-select>
          </div>

          <!-- 收藏筛选 -->
          <div class="filter-item">
            <el-checkbox v-model="showFavoritesOnly" @change="handleFilterChange">
              只显示收藏
            </el-checkbox>
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-bar">
        <span class="stats-text">
          共找到 <strong>{{ pagination.total }}</strong> 个妖怪
          <span v-if="filters.chapter">
            （{{ getChapterName(filters.chapter) }}）
          </span>
        </span>
        <div class="view-controls">
          <el-button-group>
            <el-button 
              :type="viewMode === 'grid' ? 'primary' : ''" 
              icon="el-icon-menu"
              @click="viewMode = 'grid'"
            >
              网格
            </el-button>
            <el-button 
              :type="viewMode === 'list' ? 'primary' : ''" 
              icon="el-icon-s-unfold"
              @click="viewMode = 'list'"
            >
              列表
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 妖怪列表 -->
      <div v-loading="loading" class="monsters-container">
        <div v-if="monsters.length === 0 && !loading" class="empty">
          <div class="empty-icon">👹</div>
          <div class="empty-text">没有找到符合条件的妖怪</div>
        </div>

        <div v-else :class="['monsters-grid', viewMode]">
          <MonsterCard 
            v-for="monster in monsters" 
            :key="monster.id"
            :monster="monster"
            :class="['monster-item', viewMode]"
          />
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[12, 24, 48, 96]"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import MonsterCard from '@/components/common/MonsterCard.vue'

export default {
  name: 'MonsterList',
  
  components: {
    MonsterCard
  },
  
  data() {
    return {
      monsters: [],
      loading: false,
      viewMode: 'grid', // grid | list
      filters: {
        chapter: '',
        name: '',
        sortBy: 'chapter_num,monster_num'
      },
      pagination: {
        page: 1,
        pageSize: 24,
        total: 0
      },
      showFavoritesOnly: false
    }
  },
  
  computed: {
    ...mapGetters(['chapterList', 'favoritesCount']),
    
    chapters() {
      return this.chapterList
    }
  },
  
  watch: {
    '$route.query': {
      handler(newQuery) {
        this.initFromQuery(newQuery)
        this.loadMonsters()
      },
      immediate: true
    }
  },
  
  methods: {
    // 从URL查询参数初始化
    initFromQuery(query) {
      this.filters.chapter = query.chapter || ''
      this.filters.name = query.name || ''
      this.pagination.page = parseInt(query.page) || 1
      this.pagination.pageSize = parseInt(query.pageSize) || 24
      this.showFavoritesOnly = query.favorites === 'true'
    },
    
    // 加载妖怪列表
    async loadMonsters() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          chapter: this.filters.chapter,
          name: this.filters.name,
          sortBy: this.filters.sortBy
        }
        
        const response = await this.$api.monsters.getList(params)
        
        let monsters = response.monsters
        
        // 如果只显示收藏，则过滤
        if (this.showFavoritesOnly) {
          const favorites = JSON.parse(localStorage.getItem('favorites') || '[]')
          monsters = monsters.filter(monster => favorites.includes(monster.id))
        }
        
        this.monsters = monsters
        this.pagination = response.pagination
        
      } catch (error) {
        this.handleError(error, '加载妖怪列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 处理筛选变化
    handleFilterChange() {
      this.pagination.page = 1
      this.updateQuery()
      this.loadMonsters()
    },
    
    // 处理页面大小变化
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.page = 1
      this.updateQuery()
      this.loadMonsters()
    },
    
    // 处理页面变化
    handleCurrentChange(page) {
      this.pagination.page = page
      this.updateQuery()
      this.loadMonsters()
    },
    
    // 更新URL查询参数
    updateQuery() {
      const query = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize
      }
      
      if (this.filters.chapter) query.chapter = this.filters.chapter
      if (this.filters.name) query.name = this.filters.name
      if (this.showFavoritesOnly) query.favorites = 'true'
      
      this.$router.replace({ query })
    },
    
    // 获取章节名称
    getChapterName(chapterCode) {
      const chapter = this.chapters.find(c => c.code === chapterCode)
      return chapter ? chapter.name : ''
    }
  }
}
</script>

<style lang="scss" scoped>
.monster-list {
  min-height: 100vh;
  padding: $spacing-xl 0;
}

.page-header {
  text-align: center;
  margin-bottom: $spacing-xxl;
  
  .page-title {
    font-size: 3rem;
    margin-bottom: $spacing-md;
    background: $gradient-primary;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .page-subtitle {
    font-size: $font-size-lg;
    color: $text-secondary;
    margin: 0;
  }
}

.filter-section {
  background: $bg-card;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  margin-bottom: $spacing-xl;
  border: 1px solid $border-primary;
  
  .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-lg;
    align-items: end;
  }
  
  .filter-item {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
    
    label {
      font-weight: $font-weight-medium;
      color: $text-primary;
      font-size: $font-size-sm;
    }
    
    .el-select,
    .el-input {
      width: 200px;
    }
  }
}

.stats-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
  padding: $spacing-md 0;
  border-bottom: 1px solid $border-secondary;
  
  .stats-text {
    color: $text-secondary;
    
    strong {
      color: $primary-color;
    }
  }
}

.monsters-container {
  min-height: 400px;
}

.monsters-grid {
  &.grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: $spacing-lg;
  }
  
  &.list {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
    
    .monster-item {
      .monster-card {
        display: flex;
        height: 200px;
        
        .monster-image {
          width: 200px;
          flex-shrink: 0;
        }
        
        .monster-info {
          flex: 1;
          padding: $spacing-lg;
        }
      }
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: $spacing-xxl;
  padding-top: $spacing-xl;
  border-top: 1px solid $border-secondary;
}

// 响应式设计
@media (max-width: $breakpoint-md) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
    
    .filter-item {
      .el-select,
      .el-input {
        width: 100%;
      }
    }
  }
  
  .stats-bar {
    flex-direction: column;
    gap: $spacing-md;
    align-items: stretch;
  }
  
  .monsters-grid.grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: $breakpoint-sm) {
  .page-header .page-title {
    font-size: 2rem;
  }
  
  .monsters-grid.list {
    .monster-item .monster-card {
      flex-direction: column;
      height: auto;
      
      .monster-image {
        width: 100%;
        height: 200px;
      }
    }
  }
}
</style>
