<template>
  <div class="not-found">
    <div class="container">
      <div class="error-content">
        <!-- 错误图标 -->
        <div class="error-visual">
          <div class="error-number">404</div>
          <div class="error-icon">🙈</div>
        </div>
        
        <!-- 错误信息 -->
        <div class="error-info">
          <h1 class="error-title">页面走丢了</h1>
          <p class="error-message">
            看起来这个页面被妖怪藏起来了，或者可能从来就不存在...
          </p>
          <p class="error-suggestion">
            不如去看看其他有趣的妖怪故事吧！
          </p>
        </div>
        
        <!-- 操作按钮 -->
        <div class="error-actions">
          <el-button type="primary" size="large" @click="goHome">
            <i class="el-icon-house"></i>
            返回首页
          </el-button>
          <el-button size="large" @click="goToMonsters">
            <i class="el-icon-view"></i>
            妖怪图鉴
          </el-button>
          <el-button size="large" @click="goToRandom">
            <i class="el-icon-refresh"></i>
            随机妖怪
          </el-button>
        </div>
        
        <!-- 推荐内容 -->
        <div class="recommendations">
          <h3 class="recommendations-title">或许你想要：</h3>
          <div class="recommendations-grid">
            <div class="recommendation-item" @click="goToMonsters">
              <div class="recommendation-icon">📚</div>
              <div class="recommendation-content">
                <h4>浏览妖怪图鉴</h4>
                <p>查看所有203个妖怪的详细信息</p>
              </div>
            </div>
            
            <div class="recommendation-item" @click="goToSearch">
              <div class="recommendation-icon">🔍</div>
              <div class="recommendation-content">
                <h4>搜索妖怪</h4>
                <p>通过名称、诗词或故事搜索妖怪</p>
              </div>
            </div>
            
            <div class="recommendation-item" @click="goToChapter('xiaoyao')">
              <div class="recommendation-icon">👹</div>
              <div class="recommendation-content">
                <h4>小妖章节</h4>
                <p>从小妖开始了解妖怪世界</p>
              </div>
            </div>
            
            <div class="recommendation-item" @click="goToAbout">
              <div class="recommendation-icon">ℹ️</div>
              <div class="recommendation-content">
                <h4>关于项目</h4>
                <p>了解项目背景和技术架构</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 统计信息 -->
        <div class="stats-info">
          <div class="stat-item">
            <span class="stat-number">{{ stats.total }}</span>
            <span class="stat-label">妖怪总数</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ stats.chapters.length }}</span>
            <span class="stat-label">章节数量</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ favoritesCount }}</span>
            <span class="stat-label">我的收藏</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'NotFound',
  
  computed: {
    ...mapGetters(['statsData', 'favoritesCount']),
    
    stats() {
      return this.statsData
    }
  },
  
  methods: {
    // 返回首页
    goHome() {
      this.$router.push('/')
    },
    
    // 去妖怪图鉴
    goToMonsters() {
      this.$router.push('/monsters')
    },
    
    // 去搜索页面
    goToSearch() {
      this.$router.push('/search')
    },
    
    // 去关于页面
    goToAbout() {
      this.$router.push('/about')
    },
    
    // 去章节页面
    goToChapter(chapterCode) {
      this.$router.push(`/chapter/${chapterCode}`)
    },
    
    // 随机妖怪
    async goToRandom() {
      try {
        const monster = await this.$api.monsters.getRandom()
        this.$router.push(`/monster/${monster.id}`)
      } catch (error) {
        this.handleError(error, '获取随机妖怪失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl 0;
}

.error-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.error-visual {
  margin-bottom: $spacing-xxl;
  
  .error-number {
    font-size: 8rem;
    font-weight: $font-weight-bold;
    background: $gradient-primary;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: $text-glow;
    margin-bottom: $spacing-lg;
    
    @media (max-width: $breakpoint-sm) {
      font-size: 6rem;
    }
  }
  
  .error-icon {
    font-size: 4rem;
    animation: bounce 2s infinite;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.error-info {
  margin-bottom: $spacing-xxl;
  
  .error-title {
    font-size: 2.5rem;
    color: $text-primary;
    margin-bottom: $spacing-lg;
    
    @media (max-width: $breakpoint-sm) {
      font-size: 2rem;
    }
  }
  
  .error-message {
    font-size: $font-size-lg;
    color: $text-secondary;
    margin-bottom: $spacing-md;
    line-height: $line-height-loose;
  }
  
  .error-suggestion {
    font-size: $font-size-md;
    color: $primary-color;
    margin: 0;
  }
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: $spacing-lg;
  margin-bottom: $spacing-xxl;
  
  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
    align-items: center;
  }
}

.recommendations {
  margin-bottom: $spacing-xxl;
  
  .recommendations-title {
    color: $text-primary;
    margin-bottom: $spacing-xl;
    font-size: $font-size-xl;
  }
  
  .recommendations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: $spacing-lg;
    
    @media (max-width: $breakpoint-sm) {
      grid-template-columns: 1fr;
    }
    
    .recommendation-item {
      display: flex;
      align-items: center;
      gap: $spacing-md;
      background: $bg-card;
      border-radius: $border-radius-lg;
      padding: $spacing-lg;
      border: 1px solid $border-primary;
      cursor: pointer;
      transition: all $transition-normal;
      
      &:hover {
        transform: translateY(-2px);
        border-color: $border-hover;
        box-shadow: $shadow-medium;
      }
      
      .recommendation-icon {
        font-size: 2rem;
        flex-shrink: 0;
      }
      
      .recommendation-content {
        text-align: left;
        
        h4 {
          color: $text-gold;
          margin-bottom: $spacing-xs;
          font-size: $font-size-md;
        }
        
        p {
          color: $text-secondary;
          font-size: $font-size-sm;
          margin: 0;
          line-height: $line-height-normal;
        }
      }
    }
  }
}

.stats-info {
  display: flex;
  justify-content: center;
  gap: $spacing-xl;
  
  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
    gap: $spacing-lg;
  }
  
  .stat-item {
    text-align: center;
    
    .stat-number {
      display: block;
      font-size: 2rem;
      font-weight: $font-weight-bold;
      color: $primary-color;
      text-shadow: $text-glow;
      margin-bottom: $spacing-xs;
    }
    
    .stat-label {
      color: $text-muted;
      font-size: $font-size-sm;
    }
  }
}

// 响应式设计
@media (max-width: $breakpoint-md) {
  .error-actions {
    flex-wrap: wrap;
  }
  
  .recommendations-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}
</style>
