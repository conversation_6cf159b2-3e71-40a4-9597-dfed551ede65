<template>
  <div class="home">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-overlay"></div>
      </div>
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">寻找天命人</h1>
          <p class="hero-subtitle">《黑神话：悟空》妖怪平生录</p>
          <p class="hero-description">
            203个妖怪，203首小诗，203个妖生故事<br>
            带你去看妖怪的众生相
          </p>
          <div class="hero-actions">
            <el-button type="primary" size="large" @click="goToMonsters">
              <i class="el-icon-view"></i>
              开始探索
            </el-button>
            <el-button size="large" @click="goToRandom">
              <i class="el-icon-refresh"></i>
              随机妖怪
            </el-button>
          </div>
        </div>
      </div>
    </section>

    <!-- 统计数据 -->
    <section class="stats-section">
      <div class="container">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">👹</div>
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">妖怪总数</div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">📚</div>
            <div class="stat-number">{{ stats.chapters.length }}</div>
            <div class="stat-label">章节数量</div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">📝</div>
            <div class="stat-number">203</div>
            <div class="stat-label">诗词故事</div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">⭐</div>
            <div class="stat-number">{{ favoritesCount }}</div>
            <div class="stat-label">我的收藏</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 章节导航 -->
    <section class="chapters-section">
      <div class="container">
        <h2 class="section-title">章节浏览</h2>
        <div class="chapters-grid">
          <div 
            v-for="chapter in chapters" 
            :key="chapter.code"
            class="chapter-card"
            @click="goToChapter(chapter.code)"
          >
            <div class="chapter-number">{{ chapter.chapter_num }}</div>
            <h3 class="chapter-name">{{ chapter.name }}</h3>
            <p class="chapter-description">{{ chapter.description }}</p>
            <div class="chapter-count">{{ getChapterCount(chapter.name) }} 个妖怪</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 推荐妖怪 -->
    <section class="featured-section">
      <div class="container">
        <h2 class="section-title">精选妖怪</h2>
        <div class="featured-grid">
          <MonsterCard 
            v-for="monster in featuredMonsters" 
            :key="monster.id"
            :monster="monster"
            class="featured-monster"
          />
        </div>
        <div class="section-actions">
          <el-button @click="refreshFeatured" :loading="featuredLoading">
            <i class="el-icon-refresh"></i>
            换一批
          </el-button>
          <el-button type="primary" @click="goToMonsters">
            查看全部
          </el-button>
        </div>
      </div>
    </section>

    <!-- 搜索区域 -->
    <section class="search-section">
      <div class="container">
        <div class="search-content">
          <h2 class="search-title">寻找你的天命妖怪</h2>
          <div class="search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="输入妖怪名称、诗词或故事内容..."
              size="large"
              @keyup.enter.native="handleSearch"
            >
              <el-button 
                slot="append" 
                icon="el-icon-search"
                @click="handleSearch"
              />
            </el-input>
          </div>
          <div v-if="searchHistory.length" class="search-history">
            <span class="history-label">最近搜索：</span>
            <el-tag 
              v-for="keyword in searchHistory.slice(0, 5)" 
              :key="keyword"
              @click="searchKeyword = keyword; handleSearch()"
              class="history-tag"
            >
              {{ keyword }}
            </el-tag>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import MonsterCard from '@/components/common/MonsterCard.vue'

export default {
  name: 'Home',
  
  components: {
    MonsterCard
  },
  
  data() {
    return {
      searchKeyword: '',
      featuredMonsters: [],
      featuredLoading: false
    }
  },
  
  computed: {
    ...mapGetters(['chapterList', 'statsData', 'getSearchHistory', 'favoritesCount']),
    
    chapters() {
      return this.chapterList
    },
    
    stats() {
      return this.statsData
    },
    
    searchHistory() {
      return this.getSearchHistory
    }
  },
  
  async created() {
    await this.loadFeaturedMonsters()
  },
  
  methods: {
    // 加载精选妖怪
    async loadFeaturedMonsters() {
      this.featuredLoading = true
      try {
        const response = await this.$api.monsters.getList({
          page: 1,
          pageSize: 6,
          sortBy: 'chapter_num,monster_num'
        })
        this.featuredMonsters = response.monsters
      } catch (error) {
        this.handleError(error, '加载精选妖怪失败')
      } finally {
        this.featuredLoading = false
      }
    },
    
    // 刷新精选妖怪
    async refreshFeatured() {
      this.featuredLoading = true
      try {
        // 随机获取6个妖怪
        const promises = Array(6).fill().map(() => this.$api.monsters.getRandom())
        const monsters = await Promise.all(promises)
        this.featuredMonsters = monsters
      } catch (error) {
        this.handleError(error, '刷新精选妖怪失败')
      } finally {
        this.featuredLoading = false
      }
    },
    
    // 获取章节妖怪数量
    getChapterCount(chapterName) {
      const chapter = this.stats.chapters.find(c => c.chapter === chapterName)
      return chapter ? chapter.count : 0
    },
    
    // 处理搜索
    handleSearch() {
      if (this.searchKeyword.trim()) {
        this.$router.push({
          name: 'Search',
          query: { q: this.searchKeyword.trim() }
        })
      }
    },
    
    // 导航方法
    goToMonsters() {
      this.$router.push('/monsters')
    },
    
    goToChapter(chapterCode) {
      this.$router.push(`/chapter/${chapterCode}`)
    },
    
    async goToRandom() {
      try {
        const monster = await this.$api.monsters.getRandom()
        this.$router.push(`/monster/${monster.id}`)
      } catch (error) {
        this.handleError(error, '获取随机妖怪失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
}

// 英雄区域
.hero-section {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  
  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d1810 50%, #1a1a1a 100%);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('/images/hero-pattern.png') repeat;
      opacity: 0.1;
    }
  }
  
  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
  }
  
  .hero-content {
    position: relative;
    text-align: center;
    z-index: 1;
    
    .hero-title {
      font-size: 4rem;
      font-weight: $font-weight-bold;
      color: $primary-color;
      margin-bottom: $spacing-md;
      text-shadow: $text-glow;
      animation: glow 2s ease-in-out infinite alternate;
    }
    
    .hero-subtitle {
      font-size: $font-size-xl;
      color: $text-secondary;
      margin-bottom: $spacing-lg;
    }
    
    .hero-description {
      font-size: $font-size-lg;
      color: $text-primary;
      line-height: $line-height-loose;
      margin-bottom: $spacing-xxl;
    }
    
    .hero-actions {
      display: flex;
      gap: $spacing-lg;
      justify-content: center;
    }
  }
}

@keyframes glow {
  from { text-shadow: 0 0 20px rgba(212, 175, 55, 0.5); }
  to { text-shadow: 0 0 30px rgba(212, 175, 55, 0.8), 0 0 40px rgba(212, 175, 55, 0.6); }
}

// 统计数据
.stats-section {
  padding: $spacing-xxl 0;
  background: rgba(42, 42, 42, 0.5);
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: $spacing-lg;
    
    .stat-card {
      text-align: center;
      padding: $spacing-xl;
      background: $bg-card;
      border-radius: $border-radius-lg;
      border: 1px solid $border-primary;
      transition: transform $transition-normal;
      
      &:hover {
        transform: translateY(-5px);
      }
      
      .stat-icon {
        font-size: 3rem;
        margin-bottom: $spacing-md;
      }
      
      .stat-number {
        font-size: 2.5rem;
        font-weight: $font-weight-bold;
        color: $primary-color;
        margin-bottom: $spacing-sm;
        text-shadow: $text-glow;
      }
      
      .stat-label {
        color: $text-secondary;
        font-size: $font-size-md;
      }
    }
  }
}

// 章节导航
.chapters-section {
  padding: $spacing-xxl 0;
  
  .section-title {
    text-align: center;
    margin-bottom: $spacing-xxl;
  }
  
  .chapters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: $spacing-lg;
    
    .chapter-card {
      background: $bg-card;
      border-radius: $border-radius-lg;
      padding: $spacing-xl;
      border: 1px solid $border-primary;
      cursor: pointer;
      transition: all $transition-normal;
      
      &:hover {
        transform: translateY(-5px);
        border-color: $border-hover;
        box-shadow: $shadow-heavy;
      }
      
      .chapter-number {
        font-size: 3rem;
        font-weight: $font-weight-bold;
        color: $primary-color;
        text-shadow: $text-glow;
        margin-bottom: $spacing-md;
      }
      
      .chapter-name {
        font-size: $font-size-xl;
        color: $text-gold;
        margin-bottom: $spacing-md;
      }
      
      .chapter-description {
        color: $text-secondary;
        line-height: $line-height-loose;
        margin-bottom: $spacing-lg;
      }
      
      .chapter-count {
        color: $primary-color;
        font-weight: $font-weight-medium;
      }
    }
  }
}

// 精选妖怪
.featured-section {
  padding: $spacing-xxl 0;
  background: rgba(42, 42, 42, 0.3);
  
  .section-title {
    text-align: center;
    margin-bottom: $spacing-xxl;
  }
  
  .featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: $spacing-lg;
    margin-bottom: $spacing-xl;
  }
  
  .section-actions {
    text-align: center;
    
    .el-button {
      margin: 0 $spacing-sm;
    }
  }
}

// 搜索区域
.search-section {
  padding: $spacing-xxl 0;
  
  .search-content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
    
    .search-title {
      margin-bottom: $spacing-xl;
    }
    
    .search-box {
      margin-bottom: $spacing-lg;
    }
    
    .search-history {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      gap: $spacing-sm;
      
      .history-label {
        color: $text-secondary;
        margin-right: $spacing-sm;
      }
      
      .history-tag {
        cursor: pointer;
        transition: all $transition-fast;
        
        &:hover {
          background: $primary-color;
          color: white;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: $breakpoint-md) {
  .hero-content {
    .hero-title {
      font-size: 3rem;
    }
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .chapters-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: $breakpoint-sm) {
  .hero-content {
    .hero-title {
      font-size: 2.5rem;
    }
    
    .hero-actions {
      flex-direction: column;
      align-items: center;
    }
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .featured-grid {
    grid-template-columns: 1fr;
  }
}
</style>
