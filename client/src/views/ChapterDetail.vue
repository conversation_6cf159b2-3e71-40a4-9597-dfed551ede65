<template>
  <div class="chapter-detail">
    <div class="container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <div class="spinner"></div>
        <p>正在加载章节信息...</p>
      </div>

      <!-- 章节详情 -->
      <div v-else-if="chapter" class="chapter-content">
        <!-- 面包屑导航 -->
        <el-breadcrumb separator="/" class="breadcrumb">
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/monsters' }">妖怪图鉴</el-breadcrumb-item>
          <el-breadcrumb-item>{{ chapter.name }}</el-breadcrumb-item>
        </el-breadcrumb>

        <!-- 章节头部 -->
        <div class="chapter-header">
          <div class="chapter-number">{{ chapter.chapter_num }}</div>
          <div class="chapter-info">
            <h1 class="chapter-name">{{ chapter.name }}</h1>
            <p class="chapter-description">{{ chapter.description }}</p>
            <div class="chapter-stats">
              <div class="stat-item">
                <span class="stat-number">{{ chapterMonsters.length }}</span>
                <span class="stat-label">妖怪数量</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ favoriteCount }}</span>
                <span class="stat-label">已收藏</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 筛选和排序 -->
        <div class="filter-section">
          <div class="filter-controls">
            <div class="filter-item">
              <label>搜索：</label>
              <el-input
                v-model="searchKeyword"
                placeholder="搜索本章节妖怪..."
                @input="handleFilter"
                clearable
              >
                <i slot="prefix" class="el-input__icon el-icon-search"></i>
              </el-input>
            </div>
            
            <div class="filter-item">
              <label>排序：</label>
              <el-select v-model="sortBy" @change="handleFilter">
                <el-option label="章节顺序" value="monster_num"></el-option>
                <el-option label="名称排序" value="name"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <el-checkbox v-model="showFavoritesOnly" @change="handleFilter">
                只显示收藏
              </el-checkbox>
            </div>
          </div>

          <div class="view-controls">
            <el-button-group>
              <el-button 
                :type="viewMode === 'grid' ? 'primary' : ''" 
                icon="el-icon-menu"
                @click="viewMode = 'grid'"
              >
                网格
              </el-button>
              <el-button 
                :type="viewMode === 'list' ? 'primary' : ''" 
                icon="el-icon-s-unfold"
                @click="viewMode = 'list'"
              >
                列表
              </el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 妖怪列表 -->
        <div class="monsters-section">
          <div class="section-header">
            <h3 class="section-title">
              章节妖怪
              <span class="monster-count">（{{ filteredMonsters.length }}）</span>
            </h3>
          </div>

          <div v-if="filteredMonsters.length === 0" class="empty">
            <div class="empty-icon">🔍</div>
            <div class="empty-text">没有找到符合条件的妖怪</div>
          </div>

          <div v-else :class="['monsters-grid', viewMode]">
            <MonsterCard 
              v-for="monster in filteredMonsters" 
              :key="monster.id"
              :monster="monster"
              :class="['monster-item', viewMode]"
            />
          </div>
        </div>

        <!-- 章节导航 -->
        <div class="chapter-navigation">
          <h3 class="section-title">其他章节</h3>
          <div class="chapters-grid">
            <div 
              v-for="otherChapter in otherChapters" 
              :key="otherChapter.code"
              class="chapter-card"
              @click="goToChapter(otherChapter.code)"
            >
              <div class="chapter-card-number">{{ otherChapter.chapter_num }}</div>
              <div class="chapter-card-info">
                <h4 class="chapter-card-name">{{ otherChapter.name }}</h4>
                <p class="chapter-card-desc">{{ otherChapter.description }}</p>
                <div class="chapter-card-count">{{ getChapterCount(otherChapter.name) }} 个妖怪</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-state">
        <div class="error-icon">📚</div>
        <h2>章节不存在</h2>
        <p>可能是章节代码错误，或者章节还未收录</p>
        <el-button type="primary" @click="$router.push('/')">
          返回首页
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import MonsterCard from '@/components/common/MonsterCard.vue'

export default {
  name: 'ChapterDetail',
  
  components: {
    MonsterCard
  },
  
  data() {
    return {
      chapter: null,
      chapterMonsters: [],
      loading: false,
      searchKeyword: '',
      sortBy: 'monster_num',
      showFavoritesOnly: false,
      viewMode: 'grid'
    }
  },
  
  computed: {
    ...mapGetters(['chapterList', 'getChapterByCode', 'statsData', 'isFavorite']),
    
    chapterCode() {
      return this.$route.params.code
    },
    
    otherChapters() {
      return this.chapterList.filter(c => c.code !== this.chapterCode)
    },
    
    filteredMonsters() {
      let monsters = [...this.chapterMonsters]
      
      // 搜索过滤
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        monsters = monsters.filter(monster => 
          monster.name.toLowerCase().includes(keyword) ||
          (monster.poetry && monster.poetry.toLowerCase().includes(keyword)) ||
          (monster.story && monster.story.toLowerCase().includes(keyword))
        )
      }
      
      // 收藏过滤
      if (this.showFavoritesOnly) {
        monsters = monsters.filter(monster => this.isFavorite(monster.id))
      }
      
      // 排序
      monsters.sort((a, b) => {
        if (this.sortBy === 'name') {
          return a.name.localeCompare(b.name, 'zh-CN')
        } else {
          return a.monster_num - b.monster_num
        }
      })
      
      return monsters
    },
    
    favoriteCount() {
      return this.chapterMonsters.filter(monster => this.isFavorite(monster.id)).length
    }
  },
  
  watch: {
    '$route.params.code': {
      handler(newCode) {
        if (newCode) {
          this.loadChapter()
        }
      },
      immediate: true
    }
  },
  
  methods: {
    // 加载章节信息
    async loadChapter() {
      this.loading = true
      try {
        // 获取章节信息
        this.chapter = this.getChapterByCode(this.chapterCode)
        
        if (!this.chapter) {
          this.chapter = null
          return
        }
        
        // 更新页面标题
        document.title = `${this.chapter.name} - 寻找天命人`
        
        // 加载章节妖怪
        await this.loadChapterMonsters()
        
      } catch (error) {
        console.error('加载章节失败:', error)
        this.chapter = null
      } finally {
        this.loading = false
      }
    },
    
    // 加载章节妖怪
    async loadChapterMonsters() {
      try {
        const response = await this.$api.monsters.getList({
          chapter: this.chapterCode,
          pageSize: 1000, // 获取所有妖怪
          sortBy: 'monster_num'
        })
        
        this.chapterMonsters = response.monsters
        
      } catch (error) {
        console.error('加载章节妖怪失败:', error)
        this.chapterMonsters = []
      }
    },
    
    // 处理筛选
    handleFilter() {
      // 筛选逻辑在计算属性中处理
    },
    
    // 跳转到其他章节
    goToChapter(chapterCode) {
      this.$router.push(`/chapter/${chapterCode}`)
    },
    
    // 获取章节妖怪数量
    getChapterCount(chapterName) {
      const chapterStats = this.statsData.chapters.find(c => c.chapter === chapterName)
      return chapterStats ? chapterStats.count : 0
    }
  }
}
</script>

<style lang="scss" scoped>
.chapter-detail {
  min-height: 100vh;
  padding: $spacing-xl 0;
}

.breadcrumb {
  margin-bottom: $spacing-xl;
}

.chapter-header {
  display: flex;
  align-items: center;
  gap: $spacing-xl;
  margin-bottom: $spacing-xxl;
  padding: $spacing-xl;
  background: $bg-card;
  border-radius: $border-radius-lg;
  border: 1px solid $border-primary;
  
  .chapter-number {
    font-size: 6rem;
    font-weight: $font-weight-bold;
    color: $primary-color;
    text-shadow: $text-glow;
    flex-shrink: 0;
  }
  
  .chapter-info {
    flex: 1;
    
    .chapter-name {
      font-size: 2.5rem;
      margin-bottom: $spacing-md;
      background: $gradient-primary;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .chapter-description {
      font-size: $font-size-lg;
      color: $text-secondary;
      line-height: $line-height-loose;
      margin-bottom: $spacing-lg;
    }
    
    .chapter-stats {
      display: flex;
      gap: $spacing-xl;
      
      .stat-item {
        text-align: center;
        
        .stat-number {
          display: block;
          font-size: 2rem;
          font-weight: $font-weight-bold;
          color: $primary-color;
          text-shadow: $text-glow;
        }
        
        .stat-label {
          font-size: $font-size-sm;
          color: $text-muted;
        }
      }
    }
  }
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: end;
  background: $bg-card;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  margin-bottom: $spacing-xl;
  border: 1px solid $border-primary;
  
  .filter-controls {
    display: flex;
    gap: $spacing-lg;
    align-items: end;
    
    .filter-item {
      display: flex;
      flex-direction: column;
      gap: $spacing-sm;
      
      label {
        font-weight: $font-weight-medium;
        color: $text-primary;
        font-size: $font-size-sm;
      }
      
      .el-input,
      .el-select {
        width: 200px;
      }
    }
  }
}

.monsters-section {
  margin-bottom: $spacing-xxl;
  
  .section-header {
    margin-bottom: $spacing-lg;
    
    .section-title {
      color: $text-primary;
      
      .monster-count {
        color: $primary-color;
      }
    }
  }
}

.monsters-grid {
  &.grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: $spacing-lg;
  }
  
  &.list {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
    
    .monster-item .monster-card {
      display: flex;
      height: 200px;
      
      .monster-image {
        width: 200px;
        flex-shrink: 0;
      }
      
      .monster-info {
        flex: 1;
        padding: $spacing-lg;
      }
    }
  }
}

.chapter-navigation {
  .chapters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: $spacing-lg;
    
    .chapter-card {
      display: flex;
      align-items: center;
      gap: $spacing-lg;
      background: $bg-card;
      border-radius: $border-radius-lg;
      padding: $spacing-lg;
      border: 1px solid $border-primary;
      cursor: pointer;
      transition: all $transition-normal;
      
      &:hover {
        transform: translateY(-2px);
        border-color: $border-hover;
        box-shadow: $shadow-medium;
      }
      
      .chapter-card-number {
        font-size: 3rem;
        font-weight: $font-weight-bold;
        color: $primary-color;
        text-shadow: $text-glow;
        flex-shrink: 0;
      }
      
      .chapter-card-info {
        flex: 1;
        
        .chapter-card-name {
          color: $text-gold;
          margin-bottom: $spacing-sm;
        }
        
        .chapter-card-desc {
          color: $text-secondary;
          font-size: $font-size-sm;
          margin-bottom: $spacing-sm;
        }
        
        .chapter-card-count {
          color: $primary-color;
          font-size: $font-size-sm;
          font-weight: $font-weight-medium;
        }
      }
    }
  }
}

.error-state {
  text-align: center;
  padding: $spacing-xxl;
  
  .error-icon {
    font-size: 5rem;
    margin-bottom: $spacing-lg;
  }
  
  h2 {
    color: $text-primary;
    margin-bottom: $spacing-md;
  }
  
  p {
    color: $text-secondary;
    margin-bottom: $spacing-xl;
  }
}

// 响应式设计
@media (max-width: $breakpoint-md) {
  .chapter-header {
    flex-direction: column;
    text-align: center;
    
    .chapter-number {
      font-size: 4rem;
    }
    
    .chapter-info .chapter-name {
      font-size: 2rem;
    }
  }
  
  .filter-section {
    flex-direction: column;
    gap: $spacing-lg;
    
    .filter-controls {
      flex-wrap: wrap;
      
      .filter-item .el-input,
      .filter-item .el-select {
        width: 100%;
      }
    }
  }
  
  .monsters-grid.grid {
    grid-template-columns: 1fr;
  }
  
  .chapters-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: $breakpoint-sm) {
  .chapter-header {
    .chapter-number {
      font-size: 3rem;
    }
    
    .chapter-info {
      .chapter-name {
        font-size: 1.5rem;
      }
      
      .chapter-stats {
        justify-content: center;
      }
    }
  }
  
  .monsters-grid.list .monster-item .monster-card {
    flex-direction: column;
    height: auto;
    
    .monster-image {
      width: 100%;
      height: 200px;
    }
  }
  
  .chapter-card {
    flex-direction: column;
    text-align: center;
  }
}
</style>
