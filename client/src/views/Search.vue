<template>
  <div class="search-page">
    <div class="container">
      <!-- 搜索头部 -->
      <div class="search-header">
        <h1 class="page-title">搜索妖怪</h1>
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="输入妖怪名称、诗词或故事内容..."
            size="large"
            @keyup.enter.native="handleSearch(true)"
            @input="handleSearch(false)"
            clearable
            autofocus
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="handleSearch(true)"
              :loading="searching"
            />
          </el-input>
        </div>
      </div>

      <!-- 搜索历史 -->
      <div v-if="searchHistory.length && !searchKeyword" class="search-history">
        <h3 class="history-title">搜索历史</h3>
        <div class="history-tags">
          <el-tag 
            v-for="keyword in searchHistory" 
            :key="keyword"
            @click="selectHistoryKeyword(keyword)"
            class="history-tag"
            closable
            @close="removeHistoryKeyword(keyword)"
          >
            {{ keyword }}
          </el-tag>
          <el-button 
            type="text" 
            size="small"
            @click="clearHistory"
            class="clear-history-btn"
          >
            清空历史
          </el-button>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div v-if="hasSearched" class="search-results">
        <!-- 结果统计 -->
        <div class="results-header">
          <h3 class="results-title">
            搜索结果
            <span v-if="currentKeyword" class="search-keyword">"{{ currentKeyword }}"</span>
          </h3>
          <div class="results-count">
            找到 <strong>{{ searchResults.length }}</strong> 个相关妖怪
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="searching" class="loading">
          <div class="spinner"></div>
          <p>正在搜索...</p>
        </div>

        <!-- 搜索结果列表 -->
        <div v-else-if="searchResults.length > 0" class="results-list">
          <div 
            v-for="monster in searchResults" 
            :key="monster.id"
            class="result-item"
            @click="goToDetail(monster.id)"
          >
            <div class="result-image">
              <img 
                :src="getImageUrl(monster.image)" 
                :alt="monster.name"
                @error="handleImageError"
                v-if="monster.image"
              >
              <div v-else class="image-placeholder">
                <i class="el-icon-picture"></i>
              </div>
            </div>
            
            <div class="result-content">
              <div class="result-header">
                <h4 class="result-name" v-html="highlightKeyword(monster.name)"></h4>
                <div class="result-meta">
                  <el-tag size="mini" type="warning">{{ monster.chapter }}</el-tag>
                  <span class="result-id">{{ monster.chapter_num }}.{{ monster.monster_num }}</span>
                </div>
              </div>
              
              <div v-if="monster.poetry" class="result-poetry">
                <p class="poetry-text" v-html="highlightKeyword(monster.poetry)"></p>
              </div>
              
              <div v-if="monster.story" class="result-story">
                <p class="story-text" v-html="highlightKeyword(monster.story | truncate(150))"></p>
              </div>
            </div>
            
            <div class="result-actions">
              <el-button 
                type="text" 
                icon="el-icon-view"
                @click.stop="goToDetail(monster.id)"
              >
                查看详情
              </el-button>
              <el-button 
                type="text" 
                :icon="isFavorited(monster.id) ? 'el-icon-star-on' : 'el-icon-star-off'"
                @click.stop="toggleFavorite(monster.id)"
                :style="{ color: isFavorited(monster.id) ? '#d4af37' : '#999' }"
              >
                {{ isFavorited(monster.id) ? '已收藏' : '收藏' }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 无结果状态 -->
        <div v-else class="no-results">
          <div class="no-results-icon">🔍</div>
          <h3>没有找到相关妖怪</h3>
          <p>试试其他关键词，或者浏览<router-link to="/monsters">妖怪图鉴</router-link></p>
        </div>
      </div>

      <!-- 热门搜索推荐 -->
      <div v-if="!hasSearched" class="search-suggestions">
        <h3 class="suggestions-title">热门搜索</h3>
        <div class="suggestions-grid">
          <div 
            v-for="suggestion in hotSearches" 
            :key="suggestion.keyword"
            class="suggestion-item"
            @click="selectSuggestion(suggestion.keyword)"
          >
            <div class="suggestion-icon">{{ suggestion.icon }}</div>
            <div class="suggestion-content">
              <h4 class="suggestion-keyword">{{ suggestion.keyword }}</h4>
              <p class="suggestion-desc">{{ suggestion.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Search',
  
  data() {
    return {
      searchKeyword: '',
      currentKeyword: '',
      searchResults: [],
      searching: false,
      hasSearched: false,
      searchCache: new Map(), // 搜索结果缓存
      searchTimer: null, // 防抖定时器
      hotSearches: [
        { keyword: '孙悟空', icon: '🐒', description: '齐天大圣相关' },
        { keyword: '龙', icon: '🐲', description: '龙族妖怪' },
        { keyword: '狼', icon: '🐺', description: '狼族精怪' },
        { keyword: '蜘蛛', icon: '🕷️', description: '蛛网妖精' },
        { keyword: '火', icon: '🔥', description: '火属性妖怪' },
        { keyword: '僧', icon: '👨‍🦲', description: '僧人相关' },
        { keyword: '王', icon: '👑', description: '妖王级别' },
        { keyword: '精', icon: '✨', description: '各类精怪' }
      ]
    }
  },
  
  computed: {
    ...mapGetters(['getSearchHistory', 'isFavorite']),
    
    searchHistory() {
      return this.getSearchHistory
    }
  },
  
  watch: {
    '$route.query.q': {
      handler(newKeyword) {
        if (newKeyword) {
          this.searchKeyword = newKeyword
          this.handleSearch()
        }
      },
      immediate: true
    }
  },
  
  methods: {
    // 执行搜索 - 带防抖和缓存
    async handleSearch(immediate = false) {
      const keyword = this.searchKeyword.trim()
      if (!keyword) {
        this.searchResults = []
        this.hasSearched = false
        return
      }

      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }

      // 如果不是立即搜索，使用防抖
      if (!immediate) {
        this.searchTimer = setTimeout(() => {
          this.performSearch(keyword)
        }, 500) // 500ms防抖，减少请求频率
        return
      }

      this.performSearch(keyword)
    },

    // 实际执行搜索
    async performSearch(keyword) {
      // 最小搜索长度限制
      if (keyword.length < 1) {
        this.searchResults = []
        this.hasSearched = false
        return
      }

      // 检查缓存
      if (this.searchCache.has(keyword)) {
        this.searchResults = this.searchCache.get(keyword)
        this.currentKeyword = keyword
        this.hasSearched = true
        this.$router.replace({ query: { q: keyword } })
        return
      }

      this.searching = true
      this.currentKeyword = keyword
      this.hasSearched = true

      try {
        const results = await this.$store.dispatch('searchMonsters', keyword)

        // 缓存结果（最多缓存50个搜索结果）
        if (this.searchCache.size >= 50) {
          // 删除最旧的缓存
          const firstKey = this.searchCache.keys().next().value
          this.searchCache.delete(firstKey)
        }
        this.searchCache.set(keyword, results)

        this.searchResults = results

        // 更新URL
        this.$router.replace({ query: { q: keyword } })

      } catch (error) {
        this.handleError(error, '搜索失败')
        this.searchResults = []
      } finally {
        this.searching = false
      }
    },
    
    // 选择历史关键词
    selectHistoryKeyword(keyword) {
      this.searchKeyword = keyword
      this.handleSearch(true) // 立即搜索
    },
    
    // 移除历史关键词
    removeHistoryKeyword(keyword) {
      // 这里可以添加移除单个历史记录的逻辑
      this.$message.info('已移除搜索历史')
    },
    
    // 清空搜索历史
    clearHistory() {
      this.$store.commit('CLEAR_SEARCH_HISTORY')
      this.$message.success('已清空搜索历史')
    },
    
    // 选择推荐搜索
    selectSuggestion(keyword) {
      this.searchKeyword = keyword
      this.handleSearch(true) // 立即搜索
    },
    
    // 高亮关键词
    highlightKeyword(text) {
      if (!this.currentKeyword || !text) return text
      
      const regex = new RegExp(`(${this.currentKeyword})`, 'gi')
      return text.replace(regex, '<mark class="highlight">$1</mark>')
    },
    
    // 切换收藏
    async toggleFavorite(monsterId) {
      try {
        const isFavorited = await this.$store.dispatch('toggleFavorite', monsterId)
        this.$message.success(isFavorited ? '已添加到收藏' : '已取消收藏')
      } catch (error) {
        this.handleError(error, '操作失败')
      }
    },
    
    // 检查是否收藏
    isFavorited(monsterId) {
      return this.isFavorite(monsterId)
    },
    
    // 跳转到详情页
    goToDetail(monsterId) {
      this.$router.push(`/monster/${monsterId}`)
    },
    
    // 处理图片错误
    handleImageError(event) {
      event.target.style.display = 'none'
      event.target.nextElementSibling.style.display = 'flex'
    }
  }
}
</script>

<style lang="scss" scoped>
.search-page {
  min-height: 100vh;
  padding: $spacing-xl 0;
}

.search-header {
  text-align: center;
  margin-bottom: $spacing-xxl;
  
  .page-title {
    font-size: 3rem;
    margin-bottom: $spacing-xl;
    background: $gradient-primary;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .search-box {
    max-width: 600px;
    margin: 0 auto;
  }
}

.search-history {
  margin-bottom: $spacing-xxl;
  
  .history-title {
    color: $text-primary;
    margin-bottom: $spacing-lg;
  }
  
  .history-tags {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-sm;
    align-items: center;
    
    .history-tag {
      cursor: pointer;
      transition: all $transition-fast;
      
      &:hover {
        background: $primary-color;
        color: white;
      }
    }
    
    .clear-history-btn {
      color: $text-muted;
      
      &:hover {
        color: $primary-color;
      }
    }
  }
}

.search-results {
  .results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-xl;
    padding-bottom: $spacing-md;
    border-bottom: 1px solid $border-secondary;
    
    .results-title {
      color: $text-primary;
      
      .search-keyword {
        color: $primary-color;
      }
    }
    
    .results-count {
      color: $text-secondary;
      
      strong {
        color: $primary-color;
      }
    }
  }
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
  
  .result-item {
    display: flex;
    background: $bg-card;
    border-radius: $border-radius-lg;
    padding: $spacing-lg;
    border: 1px solid $border-primary;
    cursor: pointer;
    transition: all $transition-normal;
    
    &:hover {
      transform: translateY(-2px);
      border-color: $border-hover;
      box-shadow: $shadow-medium;
    }
    
    .result-image {
      width: 120px;
      height: 120px;
      border-radius: $border-radius-md;
      overflow: hidden;
      background: rgba(0, 0, 0, 0.3);
      flex-shrink: 0;
      margin-right: $spacing-lg;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .image-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: $text-muted;
        font-size: 2rem;
      }
    }
    
    .result-content {
      flex: 1;
      
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: $spacing-md;
        
        .result-name {
          color: $text-gold;
          font-size: $font-size-lg;
          margin: 0;
        }
        
        .result-meta {
          display: flex;
          align-items: center;
          gap: $spacing-sm;
          
          .result-id {
            color: $text-muted;
            font-size: $font-size-sm;
          }
        }
      }
      
      .result-poetry {
        margin-bottom: $spacing-md;
        
        .poetry-text {
          color: $primary-color;
          font-style: italic;
          font-size: $font-size-sm;
          margin: 0;
        }
      }
      
      .result-story {
        .story-text {
          color: $text-secondary;
          font-size: $font-size-sm;
          line-height: $line-height-loose;
          margin: 0;
        }
      }
    }
    
    .result-actions {
      display: flex;
      flex-direction: column;
      gap: $spacing-sm;
      align-items: flex-end;
    }
  }
}

.no-results {
  text-align: center;
  padding: $spacing-xxl;
  
  .no-results-icon {
    font-size: 4rem;
    margin-bottom: $spacing-lg;
  }
  
  h3 {
    color: $text-primary;
    margin-bottom: $spacing-md;
  }
  
  p {
    color: $text-secondary;
    
    a {
      color: $primary-color;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.search-suggestions {
  .suggestions-title {
    color: $text-primary;
    margin-bottom: $spacing-xl;
    text-align: center;
  }
  
  .suggestions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: $spacing-lg;
    
    .suggestion-item {
      display: flex;
      align-items: center;
      background: $bg-card;
      border-radius: $border-radius-lg;
      padding: $spacing-lg;
      border: 1px solid $border-primary;
      cursor: pointer;
      transition: all $transition-normal;
      
      &:hover {
        transform: translateY(-2px);
        border-color: $border-hover;
        box-shadow: $shadow-medium;
      }
      
      .suggestion-icon {
        font-size: 2rem;
        margin-right: $spacing-md;
      }
      
      .suggestion-content {
        .suggestion-keyword {
          color: $text-gold;
          margin-bottom: $spacing-xs;
        }
        
        .suggestion-desc {
          color: $text-secondary;
          font-size: $font-size-sm;
          margin: 0;
        }
      }
    }
  }
}

// 高亮样式
:deep(.highlight) {
  background: $primary-color;
  color: white;
  padding: 2px 4px;
  border-radius: 2px;
  font-weight: $font-weight-medium;
}

// 响应式设计
@media (max-width: $breakpoint-md) {
  .results-header {
    flex-direction: column;
    gap: $spacing-sm;
    align-items: flex-start;
  }
  
  .result-item {
    flex-direction: column;
    
    .result-image {
      width: 100%;
      height: 200px;
      margin-right: 0;
      margin-bottom: $spacing-md;
    }
    
    .result-actions {
      flex-direction: row;
      justify-content: flex-end;
    }
  }
  
  .suggestions-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: $breakpoint-sm) {
  .search-header .page-title {
    font-size: 2rem;
  }
  
  .result-item .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }
}
</style>
