<template>
  <div class="about-page">
    <div class="container">
      <!-- 项目介绍 -->
      <section class="hero-section">
        <h1 class="page-title">关于项目</h1>
        <p class="page-subtitle">寻找天命人 - 《黑神话：悟空》妖怪平生录</p>
      </section>

      <!-- 项目背景 -->
      <section class="section">
        <h2 class="section-title">项目背景</h2>
        <div class="content-card">
          <p>
            《黑神话：悟空》是由游戏科学公司制作的动作角色扮演游戏，于2024年8月20日正式发售。
            这款游戏以中国古典名著《西游记》为背景，展现了一个充满东方神韵的奇幻世界。
          </p>
          <p>
            游戏中的每一个妖怪都有着独特的背景故事和深刻的文化内涵。有作者整理了《黑神话悟空妖怪平生录》
            并开源到GitHub，收录了203个妖怪，203首小诗，203个妖生故事，带我们去看妖怪的众生相。
          </p>
          <p>
            本项目将这些珍贵的文字资料数字化，通过现代化的Web技术展现给更多的玩家和文化爱好者，
            让每个妖怪的故事都能被更好地传播和保存。
          </p>
        </div>
      </section>

      <!-- 技术架构 -->
      <section class="section">
        <h2 class="section-title">技术架构</h2>
        <div class="tech-grid">
          <div class="tech-category">
            <h3 class="category-title">前端技术</h3>
            <div class="tech-list">
              <div class="tech-item">
                <div class="tech-icon">🎨</div>
                <div class="tech-info">
                  <h4>Vue.js 2.6</h4>
                  <p>渐进式JavaScript框架</p>
                </div>
              </div>
              <div class="tech-item">
                <div class="tech-icon">⚡</div>
                <div class="tech-info">
                  <h4>Element UI</h4>
                  <p>基于Vue的组件库</p>
                </div>
              </div>
              <div class="tech-item">
                <div class="tech-icon">🎯</div>
                <div class="tech-info">
                  <h4>Vue Router</h4>
                  <p>前端路由管理</p>
                </div>
              </div>
              <div class="tech-item">
                <div class="tech-icon">📦</div>
                <div class="tech-info">
                  <h4>Vuex</h4>
                  <p>状态管理</p>
                </div>
              </div>
            </div>
          </div>

          <div class="tech-category">
            <h3 class="category-title">后端技术</h3>
            <div class="tech-list">
              <div class="tech-item">
                <div class="tech-icon">🚀</div>
                <div class="tech-info">
                  <h4>Node.js</h4>
                  <p>JavaScript运行环境</p>
                </div>
              </div>
              <div class="tech-item">
                <div class="tech-icon">🌐</div>
                <div class="tech-info">
                  <h4>Express.js</h4>
                  <p>Web应用框架</p>
                </div>
              </div>
              <div class="tech-item">
                <div class="tech-icon">🗄️</div>
                <div class="tech-info">
                  <h4>SQLite</h4>
                  <p>轻量级数据库</p>
                </div>
              </div>
              <div class="tech-item">
                <div class="tech-icon">🔄</div>
                <div class="tech-info">
                  <h4>RESTful API</h4>
                  <p>标准化接口设计</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 功能特色 -->
      <section class="section">
        <h2 class="section-title">功能特色</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">📚</div>
            <h3 class="feature-title">完整收录</h3>
            <p class="feature-desc">收录203个妖怪的完整信息，包括诗词、故事和图片</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🔍</div>
            <h3 class="feature-title">智能搜索</h3>
            <p class="feature-desc">支持按名称、诗词、故事内容进行全文搜索</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📱</div>
            <h3 class="feature-title">响应式设计</h3>
            <p class="feature-desc">完美适配桌面端、平板和手机等各种设备</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">⭐</div>
            <h3 class="feature-title">收藏系统</h3>
            <p class="feature-desc">收藏喜欢的妖怪，建立个人的妖怪图鉴</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🎨</div>
            <h3 class="feature-title">主题设计</h3>
            <p class="feature-desc">黑金配色，古风元素，营造神秘的东方氛围</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🔄</div>
            <h3 class="feature-title">随机探索</h3>
            <p class="feature-desc">随机推荐妖怪，发现意想不到的精彩故事</p>
          </div>
        </div>
      </section>

      <!-- 数据统计 -->
      <section class="section">
        <h2 class="section-title">数据统计</h2>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">妖怪总数</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ stats.chapters.length }}</div>
            <div class="stat-label">章节数量</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">203</div>
            <div class="stat-label">诗词故事</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ favoritesCount }}</div>
            <div class="stat-label">用户收藏</div>
          </div>
        </div>
      </section>

      <!-- 致谢 -->
      <section class="section">
        <h2 class="section-title">致谢</h2>
        <div class="content-card">
          <ul class="thanks-list">
            <li>感谢《黑神话：悟空》游戏团队创造了如此精彩的作品</li>
            <li>感谢开源社区提供的《黑神话悟空妖怪平生录》资料</li>
            <li>感谢Vue.js、Element UI等开源项目提供的技术支持</li>
            <li>感谢所有为中华文化传承做出贡献的人们</li>
          </ul>
        </div>
      </section>

      <!-- 联系方式 -->
      <section class="section">
        <h2 class="section-title">联系我们</h2>
        <div class="contact-grid">
          <div class="contact-card">
            <div class="contact-icon">📧</div>
            <h3>邮箱联系</h3>
            <p><EMAIL></p>
          </div>
          <div class="contact-card">
            <div class="contact-icon">💻</div>
            <h3>项目地址</h3>
            <p>
              <a href="https://github.com" target="_blank" rel="noopener">
                GitHub Repository
              </a>
            </p>
          </div>
          <div class="contact-card">
            <div class="contact-icon">🌟</div>
            <h3>支持项目</h3>
            <p>给项目点个Star，分享给更多人</p>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'About',
  
  computed: {
    ...mapGetters(['statsData', 'favoritesCount']),
    
    stats() {
      return this.statsData
    }
  }
}
</script>

<style lang="scss" scoped>
.about-page {
  min-height: 100vh;
  padding: $spacing-xl 0;
}

.hero-section {
  text-align: center;
  margin-bottom: $spacing-xxl;
  
  .page-title {
    font-size: 3rem;
    margin-bottom: $spacing-md;
    background: $gradient-primary;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .page-subtitle {
    font-size: $font-size-xl;
    color: $text-secondary;
    margin: 0;
  }
}

.section {
  margin-bottom: $spacing-xxl;
  
  .section-title {
    font-size: 2rem;
    color: $primary-color;
    margin-bottom: $spacing-xl;
    text-align: center;
    text-shadow: $text-glow;
  }
}

.content-card {
  background: $bg-card;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  border: 1px solid $border-primary;
  
  p {
    font-size: $font-size-md;
    line-height: $line-height-loose;
    color: $text-primary;
    margin-bottom: $spacing-lg;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: $spacing-xl;
  
  .tech-category {
    background: $bg-card;
    border-radius: $border-radius-lg;
    padding: $spacing-xl;
    border: 1px solid $border-primary;
    
    .category-title {
      color: $text-gold;
      margin-bottom: $spacing-lg;
      text-align: center;
    }
    
    .tech-list {
      display: flex;
      flex-direction: column;
      gap: $spacing-lg;
      
      .tech-item {
        display: flex;
        align-items: center;
        gap: $spacing-md;
        
        .tech-icon {
          font-size: 2rem;
          flex-shrink: 0;
        }
        
        .tech-info {
          h4 {
            color: $text-primary;
            margin-bottom: $spacing-xs;
          }
          
          p {
            color: $text-secondary;
            font-size: $font-size-sm;
            margin: 0;
          }
        }
      }
    }
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: $spacing-lg;
  
  .feature-card {
    background: $bg-card;
    border-radius: $border-radius-lg;
    padding: $spacing-xl;
    border: 1px solid $border-primary;
    text-align: center;
    transition: transform $transition-normal;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    .feature-icon {
      font-size: 3rem;
      margin-bottom: $spacing-lg;
    }
    
    .feature-title {
      color: $text-gold;
      margin-bottom: $spacing-md;
    }
    
    .feature-desc {
      color: $text-secondary;
      line-height: $line-height-loose;
      margin: 0;
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-lg;
  
  .stat-card {
    background: $bg-card;
    border-radius: $border-radius-lg;
    padding: $spacing-xl;
    border: 1px solid $border-primary;
    text-align: center;
    
    .stat-number {
      font-size: 3rem;
      font-weight: $font-weight-bold;
      color: $primary-color;
      text-shadow: $text-glow;
      display: block;
      margin-bottom: $spacing-md;
    }
    
    .stat-label {
      color: $text-secondary;
      font-size: $font-size-md;
    }
  }
}

.thanks-list {
  list-style: none;
  padding: 0;
  
  li {
    position: relative;
    padding-left: $spacing-lg;
    margin-bottom: $spacing-md;
    color: $text-primary;
    line-height: $line-height-loose;
    
    &:before {
      content: '✨';
      position: absolute;
      left: 0;
      top: 0;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: $spacing-lg;
  
  .contact-card {
    background: $bg-card;
    border-radius: $border-radius-lg;
    padding: $spacing-xl;
    border: 1px solid $border-primary;
    text-align: center;
    
    .contact-icon {
      font-size: 3rem;
      margin-bottom: $spacing-lg;
    }
    
    h3 {
      color: $text-gold;
      margin-bottom: $spacing-md;
    }
    
    p {
      color: $text-secondary;
      margin: 0;
      
      a {
        color: $primary-color;
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: $breakpoint-md) {
  .hero-section .page-title {
    font-size: 2rem;
  }
  
  .tech-grid {
    grid-template-columns: 1fr;
  }
  
  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: $breakpoint-sm) {
  .hero-section .page-title {
    font-size: 1.5rem;
  }
  
  .section .section-title {
    font-size: 1.5rem;
  }
  
  .features-grid,
  .contact-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
