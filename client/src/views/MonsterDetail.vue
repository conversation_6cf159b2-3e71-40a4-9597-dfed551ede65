<template>
  <div class="monster-detail">
    <div class="container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <div class="spinner"></div>
        <p>正在加载妖怪详情...</p>
      </div>

      <!-- 妖怪详情 -->
      <div v-else-if="monster" class="detail-content">
        <!-- 面包屑导航 -->
        <el-breadcrumb separator="/" class="breadcrumb">
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/monsters' }">妖怪图鉴</el-breadcrumb-item>
          <el-breadcrumb-item>{{ monster.name }}</el-breadcrumb-item>
        </el-breadcrumb>

        <!-- 妖怪主要信息 -->
        <div class="monster-main">
          <div class="monster-image-section">
            <div class="image-container">
              <img 
                :src="getImageUrl(monster.image)" 
                :alt="monster.name"
                @error="handleImageError"
                v-if="monster.image"
              >
              <div v-else class="image-placeholder">
                <i class="el-icon-picture"></i>
                <p>暂无图片</p>
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons">
              <el-button 
                :type="isFavorited ? 'warning' : 'primary'"
                @click="toggleFavorite"
                :icon="isFavorited ? 'el-icon-star-on' : 'el-icon-star-off'"
              >
                {{ isFavorited ? '取消收藏' : '收藏' }}
              </el-button>
              <el-button @click="goToRandom" icon="el-icon-refresh">
                随机妖怪
              </el-button>
            </div>
          </div>

          <div class="monster-info-section">
            <div class="monster-header">
              <h1 class="monster-name">{{ monster.name }}</h1>
              <div class="monster-meta">
                <el-tag type="warning">{{ monster.chapter }}</el-tag>
                <span class="monster-id">{{ monster.chapter_num }}.{{ monster.monster_num }}</span>
              </div>
            </div>

            <!-- 诗词部分 -->
            <div v-if="monster.poetry" class="poetry-section">
              <h3 class="section-title">
                <i class="el-icon-edit"></i>
                诗词
              </h3>
              <div class="poetry-content">
                <p v-for="(line, index) in poetryLines" :key="index" class="poetry-line">
                  {{ line }}
                </p>
              </div>
            </div>

            <!-- 故事部分 -->
            <div v-if="monster.story" class="story-section">
              <h3 class="section-title">
                <i class="el-icon-document"></i>
                平生录
              </h3>
              <div class="story-content">
                <p v-for="(paragraph, index) in storyParagraphs" :key="index" class="story-paragraph">
                  {{ paragraph }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 相关妖怪推荐 -->
        <div class="related-section">
          <h3 class="section-title">
            <i class="el-icon-connection"></i>
            同章节妖怪
          </h3>
          <div v-loading="relatedLoading" class="related-monsters">
            <MonsterCard 
              v-for="relatedMonster in relatedMonsters" 
              :key="relatedMonster.id"
              :monster="relatedMonster"
              class="related-monster"
            />
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-state">
        <div class="error-icon">😵</div>
        <h2>妖怪不见了</h2>
        <p>可能是被天命人收服了，或者妖怪ID不存在</p>
        <el-button type="primary" @click="$router.push('/monsters')">
          返回妖怪图鉴
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import MonsterCard from '@/components/common/MonsterCard.vue'

export default {
  name: 'MonsterDetail',
  
  components: {
    MonsterCard
  },
  
  data() {
    return {
      monster: null,
      loading: false,
      relatedMonsters: [],
      relatedLoading: false
    }
  },
  
  computed: {
    ...mapGetters(['isFavorite']),
    
    monsterId() {
      return this.$route.params.id
    },
    
    isFavorited() {
      return this.monster ? this.isFavorite(this.monster.id) : false
    },
    
    poetryLines() {
      if (!this.monster || !this.monster.poetry) return []
      return this.monster.poetry.split('\n').filter(line => line.trim())
    },

    storyParagraphs() {
      if (!this.monster || !this.monster.story) return []
      return this.monster.story.split('\n').filter(p => p.trim())
    }
  },
  
  watch: {
    '$route.params.id': {
      handler(newId) {
        if (newId) {
          this.loadMonster()
        }
      },
      immediate: true
    }
  },
  
  methods: {
    // 加载妖怪详情
    async loadMonster() {
      this.loading = true
      try {
        this.monster = await this.$api.monsters.getById(this.monsterId)
        
        // 更新页面标题
        document.title = `${this.monster.name} - 寻找天命人`
        
        // 加载相关妖怪
        this.loadRelatedMonsters()
        
      } catch (error) {
        console.error('加载妖怪详情失败:', error)
        this.monster = null
      } finally {
        this.loading = false
      }
    },
    
    // 加载相关妖怪
    async loadRelatedMonsters() {
      if (!this.monster) return
      
      this.relatedLoading = true
      try {
        const response = await this.$api.monsters.getList({
          chapter: this.monster.chapter_code,
          pageSize: 6
        })
        
        // 排除当前妖怪
        this.relatedMonsters = response.monsters.filter(m => m.id !== this.monster.id)
        
      } catch (error) {
        console.error('加载相关妖怪失败:', error)
      } finally {
        this.relatedLoading = false
      }
    },
    
    // 切换收藏状态
    async toggleFavorite() {
      try {
        const isFavorited = await this.$store.dispatch('toggleFavorite', this.monster.id)
        this.$message.success(isFavorited ? '已添加到收藏' : '已取消收藏')
      } catch (error) {
        this.handleError(error, '操作失败')
      }
    },
    
    // 随机妖怪
    async goToRandom() {
      try {
        const monster = await this.$api.monsters.getRandom()
        this.$router.push(`/monster/${monster.id}`)
      } catch (error) {
        this.handleError(error, '获取随机妖怪失败')
      }
    },
    
    // 处理图片错误
    handleImageError(event) {
      event.target.style.display = 'none'
      event.target.nextElementSibling.style.display = 'flex'
    }
  }
}
</script>

<style lang="scss" scoped>
.monster-detail {
  min-height: 100vh;
  padding: $spacing-xl 0;
}

.breadcrumb {
  margin-bottom: $spacing-xl;
}

.monster-main {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: $spacing-xxl;
  margin-bottom: $spacing-xxl;
  
  @media (max-width: $breakpoint-md) {
    grid-template-columns: 1fr;
    gap: $spacing-xl;
  }
}

.monster-image-section {
  .image-container {
    width: 100%;
    height: 400px;
    border-radius: $border-radius-lg;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.3);
    margin-bottom: $spacing-lg;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .image-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: $text-muted;
      
      i {
        font-size: 4rem;
        margin-bottom: $spacing-md;
      }
    }
  }
  
  .action-buttons {
    display: flex;
    gap: $spacing-md;
    
    .el-button {
      flex: 1;
    }
  }
}

.monster-info-section {
  .monster-header {
    margin-bottom: $spacing-xxl;
    
    .monster-name {
      font-size: 3rem;
      margin-bottom: $spacing-md;
      background: $gradient-primary;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .monster-meta {
      display: flex;
      align-items: center;
      gap: $spacing-md;
      
      .monster-id {
        color: $text-secondary;
        font-weight: $font-weight-medium;
      }
    }
  }
}

.section-title {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  font-size: $font-size-xl;
  color: $primary-color;
  margin-bottom: $spacing-lg;
  
  i {
    font-size: $font-size-lg;
  }
}

.poetry-section {
  margin-bottom: $spacing-xxl;
  
  .poetry-content {
    background: $bg-card;
    border-radius: $border-radius-lg;
    padding: $spacing-xl;
    border-left: 4px solid $primary-color;
    
    .poetry-line {
      font-size: $font-size-lg;
      line-height: $line-height-loose;
      color: $primary-color;
      font-style: italic;
      text-align: center;
      margin-bottom: $spacing-md;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.story-section {
  .story-content {
    background: $bg-card;
    border-radius: $border-radius-lg;
    padding: $spacing-xl;
    border: 1px solid $border-primary;
    
    .story-paragraph {
      font-size: $font-size-md;
      line-height: $line-height-loose;
      color: $text-primary;
      margin-bottom: $spacing-lg;
      text-indent: 2em;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.related-section {
  .related-monsters {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: $spacing-lg;
    
    @media (max-width: $breakpoint-sm) {
      grid-template-columns: 1fr;
    }
  }
}

.error-state {
  text-align: center;
  padding: $spacing-xxl;
  
  .error-icon {
    font-size: 5rem;
    margin-bottom: $spacing-lg;
  }
  
  h2 {
    color: $text-primary;
    margin-bottom: $spacing-md;
  }
  
  p {
    color: $text-secondary;
    margin-bottom: $spacing-xl;
  }
}

// 响应式设计
@media (max-width: $breakpoint-sm) {
  .monster-info-section .monster-header .monster-name {
    font-size: 2rem;
  }
  
  .poetry-content,
  .story-content {
    padding: $spacing-lg;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
</style>
