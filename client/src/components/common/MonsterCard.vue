<template>
  <div class="monster-card" @click="goToDetail">
    <!-- 妖怪图片 -->
    <div class="monster-image">
      <img 
        :src="getImageUrl(monster.image)" 
        :alt="monster.name"
        @error="handleImageError"
        v-if="monster.image"
      >
      <div v-else class="image-placeholder">
        <i class="el-icon-picture"></i>
      </div>
      
      <!-- 收藏按钮 -->
      <div class="favorite-btn" @click.stop="toggleFavorite">
        <i :class="favoriteIcon" :style="{ color: isFavorited ? '#d4af37' : '#999' }"></i>
      </div>
      
      <!-- 章节标签 -->
      <div class="chapter-tag">
        {{ monster.chapter }}
      </div>
    </div>
    
    <!-- 妖怪信息 -->
    <div class="monster-info">
      <h3 class="monster-name">{{ monster.name }}</h3>
      
      <!-- 诗词预览 -->
      <div v-if="monster.poetry" class="monster-poetry">
        <p class="poetry-text">{{ formatPoetry(monster.poetry) }}</p>
      </div>
      
      <!-- 故事预览 -->
      <div v-if="monster.story" class="monster-story">
        <p class="story-text">{{ monster.story | truncate(100) }}</p>
      </div>
      
      <!-- 元信息 -->
      <div class="monster-meta">
        <span class="meta-item">
          <i class="el-icon-collection-tag"></i>
          {{ monster.chapter_num }}.{{ monster.monster_num }}
        </span>
        <span class="meta-item">
          <i class="el-icon-view"></i>
          查看详情
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'MonsterCard',
  
  props: {
    monster: {
      type: Object,
      required: true
    }
  },
  
  computed: {
    ...mapGetters(['isFavorite']),
    
    isFavorited() {
      return this.isFavorite(this.monster.id)
    },
    
    favoriteIcon() {
      return this.isFavorited ? 'el-icon-star-on' : 'el-icon-star-off'
    }
  },
  
  methods: {
    // 格式化诗词显示
    formatPoetry(poetry) {
      if (!poetry) return ''
      
      // 取前两行诗词
      const lines = poetry.split('\n').filter(line => line.trim())
      return lines.slice(0, 2).join(' / ')
    },
    
    // 处理图片加载错误
    handleImageError(event) {
      event.target.style.display = 'none'
      event.target.nextElementSibling.style.display = 'flex'
    },
    
    // 切换收藏状态
    async toggleFavorite() {
      try {
        const isFavorited = await this.$store.dispatch('toggleFavorite', this.monster.id)
        this.$message.success(isFavorited ? '已添加到收藏' : '已取消收藏')
      } catch (error) {
        this.handleError(error, '操作失败')
      }
    },
    
    // 跳转到详情页
    goToDetail() {
      this.$router.push(`/monster/${this.monster.id}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.monster-card {
  background: $bg-card;
  border-radius: $border-radius-lg;
  overflow: hidden;
  border: 1px solid $border-primary;
  cursor: pointer;
  transition: all $transition-normal;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  &:hover {
    transform: translateY(-5px);
    border-color: $border-hover;
    box-shadow: $shadow-heavy;
    
    .monster-image img {
      transform: scale(1.05);
    }
  }
}

.monster-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.3);
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform $transition-normal;
  }
  
  .image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.2);
    color: $text-muted;
    font-size: 3rem;
  }
  
  .favorite-btn {
    position: absolute;
    top: $spacing-md;
    right: $spacing-md;
    width: 36px;
    height: 36px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all $transition-fast;
    z-index: 2;
    
    &:hover {
      background: rgba(0, 0, 0, 0.8);
      transform: scale(1.1);
    }
    
    i {
      font-size: $font-size-lg;
      transition: color $transition-fast;
    }
  }
  
  .chapter-tag {
    position: absolute;
    top: $spacing-md;
    left: $spacing-md;
    background: rgba(212, 175, 55, 0.9);
    color: white;
    padding: $spacing-xs $spacing-sm;
    border-radius: $border-radius-sm;
    font-size: $font-size-xs;
    font-weight: $font-weight-medium;
  }
}

.monster-info {
  padding: $spacing-lg;
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .monster-name {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-gold;
    margin-bottom: $spacing-md;
    text-align: center;
  }
  
  .monster-poetry {
    margin-bottom: $spacing-md;
    
    .poetry-text {
      font-style: italic;
      color: $primary-color;
      font-size: $font-size-sm;
      line-height: $line-height-loose;
      text-align: center;
      border-left: 2px solid $primary-color;
      padding-left: $spacing-sm;
      margin: 0;
    }
  }
  
  .monster-story {
    margin-bottom: $spacing-md;
    flex: 1;
    
    .story-text {
      color: $text-secondary;
      font-size: $font-size-sm;
      line-height: $line-height-loose;
      margin: 0;
    }
  }
  
  .monster-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: $spacing-md;
    border-top: 1px solid $border-secondary;
    margin-top: auto;
    
    .meta-item {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      font-size: $font-size-sm;
      color: $text-muted;
      
      &:last-child {
        color: $primary-color;
        font-weight: $font-weight-medium;
      }
      
      i {
        font-size: $font-size-sm;
      }
    }
  }
}

// 响应式设计
@media (max-width: $breakpoint-sm) {
  .monster-image {
    height: 150px;
  }
  
  .monster-info {
    padding: $spacing-md;
    
    .monster-name {
      font-size: $font-size-md;
    }
    
    .monster-poetry,
    .monster-story {
      .poetry-text,
      .story-text {
        font-size: $font-size-xs;
      }
    }
  }
}
</style>
