<template>
  <header class="app-header">
    <div class="container">
      <div class="header-content">
        <!-- Logo和标题 -->
        <div class="header-left">
          <router-link to="/" class="logo-link">
            <div class="logo">
              <img src="/favicon.ico" alt="Logo" class="logo-icon">
              <div class="logo-text">
                <h1 class="title">寻找天命人</h1>
                <p class="subtitle">黑神话悟空妖怪平生录</p>
              </div>
            </div>
          </router-link>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="header-nav">
          <router-link to="/" class="nav-item" exact>首页</router-link>
          <router-link to="/monsters" class="nav-item">妖怪图鉴</router-link>
          <el-dropdown @command="handleChapterSelect" class="chapter-dropdown">
            <span class="nav-item dropdown-trigger">
              章节浏览 <i class="el-icon-arrow-down"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item 
                v-for="chapter in chapters" 
                :key="chapter.code"
                :command="chapter.code"
              >
                {{ chapter.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <router-link to="/about" class="nav-item">关于</router-link>
        </nav>
        
        <!-- 搜索和工具 -->
        <div class="header-right">
          <!-- 搜索框 -->
          <div class="search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索妖怪..."
              prefix-icon="el-icon-search"
              @keyup.enter.native="handleSearch"
              @focus="showSearchSuggestions = true"
              @blur="hideSearchSuggestions"
              clearable
            />
            
            <!-- 搜索建议 -->
            <div v-if="showSearchSuggestions && searchSuggestions.length" class="search-suggestions">
              <div 
                v-for="suggestion in searchSuggestions" 
                :key="suggestion"
                class="suggestion-item"
                @click="selectSuggestion(suggestion)"
              >
                {{ suggestion }}
              </div>
            </div>
          </div>
          
          <!-- 收藏按钮 -->
          <el-button 
            type="text" 
            class="favorites-btn"
            @click="showFavorites"
          >
            <i class="el-icon-star-on"></i>
            <span class="favorites-count">{{ favoritesCount }}</span>
          </el-button>
          
          <!-- 随机妖怪按钮 -->
          <el-button 
            type="primary" 
            size="small"
            @click="goToRandomMonster"
            :loading="randomLoading"
          >
            <i class="el-icon-refresh"></i>
            随机
          </el-button>
        </div>
        
        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-btn" @click="toggleMobileMenu">
          <i class="el-icon-menu"></i>
        </div>
      </div>
    </div>
    
    <!-- 移动端菜单 -->
    <div v-if="showMobileMenu" class="mobile-menu">
      <router-link to="/" class="mobile-nav-item" @click="closeMobileMenu">首页</router-link>
      <router-link to="/monsters" class="mobile-nav-item" @click="closeMobileMenu">妖怪图鉴</router-link>
      <div class="mobile-chapters">
        <div class="mobile-chapter-title">章节浏览</div>
        <router-link 
          v-for="chapter in chapters" 
          :key="chapter.code"
          :to="`/chapter/${chapter.code}`"
          class="mobile-chapter-item"
          @click="closeMobileMenu"
        >
          {{ chapter.name }}
        </router-link>
      </div>
      <router-link to="/about" class="mobile-nav-item" @click="closeMobileMenu">关于</router-link>
    </div>
  </header>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'AppHeader',
  
  data() {
    return {
      searchKeyword: '',
      showSearchSuggestions: false,
      showMobileMenu: false,
      randomLoading: false
    }
  },
  
  computed: {
    ...mapGetters(['chapterList', 'getSearchHistory', 'favoritesCount']),
    
    chapters() {
      return this.chapterList
    },
    
    searchSuggestions() {
      if (!this.searchKeyword) {
        return this.getSearchHistory
      }
      return this.getSearchHistory.filter(item => 
        item.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    }
  },
  
  methods: {
    // 处理搜索
    handleSearch() {
      if (this.searchKeyword.trim()) {
        this.$router.push({
          name: 'Search',
          query: { q: this.searchKeyword.trim() }
        })
        this.hideSearchSuggestions()
      }
    },
    
    // 选择搜索建议
    selectSuggestion(suggestion) {
      this.searchKeyword = suggestion
      this.handleSearch()
    },
    
    // 隐藏搜索建议
    hideSearchSuggestions() {
      setTimeout(() => {
        this.showSearchSuggestions = false
      }, 200)
    },
    
    // 处理章节选择
    handleChapterSelect(chapterCode) {
      this.$router.push(`/chapter/${chapterCode}`)
    },
    
    // 显示收藏
    showFavorites() {
      this.$router.push('/monsters?favorites=true')
    },
    
    // 随机妖怪
    async goToRandomMonster() {
      this.randomLoading = true
      try {
        const monster = await this.$api.monsters.getRandom()
        this.$router.push(`/monster/${monster.id}`)
      } catch (error) {
        this.handleError(error, '获取随机妖怪失败')
      } finally {
        this.randomLoading = false
      }
    },
    
    // 切换移动端菜单
    toggleMobileMenu() {
      this.showMobileMenu = !this.showMobileMenu
    },
    
    // 关闭移动端菜单
    closeMobileMenu() {
      this.showMobileMenu = false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: $header-height;
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid $border-primary;
  z-index: $z-index-fixed;
  
  .header-content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.header-left {
  .logo-link {
    text-decoration: none;
    color: inherit;
  }
  
  .logo {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    
    .logo-icon {
      width: 40px;
      height: 40px;
    }
    
    .logo-text {
      .title {
        font-size: $font-size-xl;
        font-weight: $font-weight-bold;
        color: $primary-color;
        margin: 0;
        text-shadow: $text-glow;
      }
      
      .subtitle {
        font-size: $font-size-sm;
        color: $text-secondary;
        margin: 0;
      }
    }
  }
}

.header-nav {
  display: flex;
  align-items: center;
  gap: $spacing-xl;
  
  .nav-item {
    color: $text-primary;
    text-decoration: none;
    font-weight: $font-weight-medium;
    transition: color $transition-fast;
    cursor: pointer;
    
    &:hover,
    &.router-link-active {
      color: $primary-color;
    }
  }
  
  .dropdown-trigger {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  
  .search-box {
    position: relative;
    width: 250px;
    
    .search-suggestions {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: $bg-secondary;
      border: 1px solid $border-primary;
      border-radius: $border-radius-md;
      box-shadow: $shadow-medium;
      z-index: $z-index-dropdown;
      max-height: 200px;
      overflow-y: auto;
      
      .suggestion-item {
        padding: $spacing-sm $spacing-md;
        cursor: pointer;
        transition: background $transition-fast;
        
        &:hover {
          background: rgba(212, 175, 55, 0.1);
        }
      }
    }
  }
  
  .favorites-btn {
    color: $primary-color;
    
    .favorites-count {
      margin-left: $spacing-xs;
      background: $primary-color;
      color: white;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: $font-size-xs;
    }
  }
}

.mobile-menu-btn {
  display: none;
  font-size: $font-size-xl;
  color: $text-primary;
  cursor: pointer;
  
  &:hover {
    color: $primary-color;
  }
}

.mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(26, 26, 26, 0.98);
  border-bottom: 1px solid $border-primary;
  padding: $spacing-lg;
  
  .mobile-nav-item {
    display: block;
    color: $text-primary;
    text-decoration: none;
    padding: $spacing-md 0;
    border-bottom: 1px solid $border-secondary;
    
    &:hover,
    &.router-link-active {
      color: $primary-color;
    }
  }
  
  .mobile-chapters {
    .mobile-chapter-title {
      color: $primary-color;
      font-weight: $font-weight-semibold;
      padding: $spacing-md 0;
      border-bottom: 1px solid $border-secondary;
    }
    
    .mobile-chapter-item {
      display: block;
      color: $text-secondary;
      text-decoration: none;
      padding: $spacing-sm $spacing-md;
      
      &:hover {
        color: $primary-color;
      }
    }
  }
}

// 响应式设计
@media (max-width: $breakpoint-md) {
  .header-nav {
    display: none;
  }
  
  .header-right {
    .search-box {
      width: 200px;
    }
  }
  
  .mobile-menu-btn {
    display: block;
  }
  
  .mobile-menu {
    display: block;
  }
}

@media (max-width: $breakpoint-sm) {
  .header-left {
    .logo-text {
      .subtitle {
        display: none;
      }
    }
  }
  
  .header-right {
    .search-box {
      width: 150px;
    }
    
    .favorites-btn span {
      display: none;
    }
  }
}
</style>
