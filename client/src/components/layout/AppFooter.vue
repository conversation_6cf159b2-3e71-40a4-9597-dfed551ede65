<template>
  <footer class="app-footer">
    <div class="container">
      <div class="footer-content">
        <!-- 项目信息 -->
        <div class="footer-section">
          <h3 class="section-title">寻找天命人</h3>
          <p class="section-desc">
            《黑神话：悟空》妖怪平生录，203个妖怪，203首小诗，203个妖生故事，
            带你去看妖怪的众生相。
          </p>
          <div class="project-stats">
            <div class="stat-item">
              <span class="stat-number">{{ stats.total }}</span>
              <span class="stat-label">妖怪总数</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ stats.chapters.length }}</span>
              <span class="stat-label">章节数量</span>
            </div>
          </div>
        </div>
        
        <!-- 快速导航 -->
        <div class="footer-section">
          <h3 class="section-title">快速导航</h3>
          <ul class="nav-list">
            <li><router-link to="/">首页</router-link></li>
            <li><router-link to="/monsters">妖怪图鉴</router-link></li>
            <li><router-link to="/search">搜索妖怪</router-link></li>
            <li><router-link to="/about">关于项目</router-link></li>
          </ul>
        </div>
        
        <!-- 章节导航 -->
        <div class="footer-section">
          <h3 class="section-title">章节浏览</h3>
          <ul class="nav-list">
            <li v-for="chapter in chapters" :key="chapter.code">
              <router-link :to="`/chapter/${chapter.code}`">
                {{ chapter.name }}
              </router-link>
            </li>
          </ul>
        </div>
        
        <!-- 技术信息 -->
        <div class="footer-section">
          <h3 class="section-title">技术栈</h3>
          <div class="tech-stack">
            <div class="tech-item">
              <i class="tech-icon">🎮</i>
              <span>Vue.js</span>
            </div>
            <div class="tech-item">
              <i class="tech-icon">⚡</i>
              <span>Element UI</span>
            </div>
            <div class="tech-item">
              <i class="tech-icon">🗄️</i>
              <span>SQLite</span>
            </div>
            <div class="tech-item">
              <i class="tech-icon">🚀</i>
              <span>Node.js</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 版权信息 -->
      <div class="footer-bottom">
        <div class="copyright">
          <p>
            © 2024 寻找天命人项目组. 
            基于开源的《黑神话悟空妖怪平生录》制作
          </p>
          <p class="disclaimer">
            本项目仅供学习交流使用，所有内容版权归原作者所有
          </p>
        </div>
        
        <div class="footer-links">
          <a href="https://github.com" target="_blank" rel="noopener">
            <i class="el-icon-link"></i>
            GitHub
          </a>
          <a href="#" @click.prevent="showContact">
            <i class="el-icon-message"></i>
            联系我们
          </a>
        </div>
      </div>
    </div>
    
    <!-- 联系对话框 -->
    <el-dialog
      title="联系我们"
      :visible.sync="contactDialogVisible"
      width="400px"
      center
    >
      <div class="contact-content">
        <p>如果您有任何问题或建议，欢迎联系我们：</p>
        <div class="contact-item">
          <strong>邮箱：</strong>
          <span><EMAIL></span>
        </div>
        <div class="contact-item">
          <strong>项目地址：</strong>
          <a href="https://github.com" target="_blank">GitHub Repository</a>
        </div>
      </div>
    </el-dialog>
  </footer>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'AppFooter',
  
  data() {
    return {
      contactDialogVisible: false
    }
  },
  
  computed: {
    ...mapGetters(['chapterList', 'statsData']),
    
    chapters() {
      return this.chapterList
    },
    
    stats() {
      return this.statsData
    }
  },
  
  methods: {
    showContact() {
      this.contactDialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.app-footer {
  background: linear-gradient(135deg, #1a1a1a 0%, #0d0d0d 100%);
  border-top: 1px solid $border-primary;
  padding: $spacing-xxl 0 $spacing-lg;
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: $spacing-xl;
  margin-bottom: $spacing-xl;
  
  @media (max-width: $breakpoint-md) {
    grid-template-columns: 1fr 1fr;
    gap: $spacing-lg;
  }
  
  @media (max-width: $breakpoint-sm) {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }
}

.footer-section {
  .section-title {
    color: $primary-color;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    margin-bottom: $spacing-md;
    text-shadow: $text-glow;
  }
  
  .section-desc {
    color: $text-secondary;
    line-height: $line-height-loose;
    margin-bottom: $spacing-lg;
  }
}

.project-stats {
  display: flex;
  gap: $spacing-lg;
  
  .stat-item {
    text-align: center;
    
    .stat-number {
      display: block;
      font-size: $font-size-xxl;
      font-weight: $font-weight-bold;
      color: $primary-color;
      text-shadow: $text-glow;
    }
    
    .stat-label {
      font-size: $font-size-sm;
      color: $text-muted;
    }
  }
}

.nav-list {
  list-style: none;
  
  li {
    margin-bottom: $spacing-sm;
    
    a {
      color: $text-secondary;
      text-decoration: none;
      transition: color $transition-fast;
      
      &:hover {
        color: $primary-color;
      }
    }
  }
}

.tech-stack {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
  
  .tech-item {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    color: $text-secondary;
    
    .tech-icon {
      font-size: $font-size-lg;
    }
  }
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: $spacing-lg;
  border-top: 1px solid $border-secondary;
  
  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
    gap: $spacing-md;
    text-align: center;
  }
}

.copyright {
  color: $text-muted;
  font-size: $font-size-sm;
  
  .disclaimer {
    margin-top: $spacing-xs;
    font-size: $font-size-xs;
    opacity: 0.8;
  }
}

.footer-links {
  display: flex;
  gap: $spacing-lg;
  
  a {
    color: $text-secondary;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    transition: color $transition-fast;
    
    &:hover {
      color: $primary-color;
    }
  }
}

.contact-content {
  .contact-item {
    margin-bottom: $spacing-md;
    
    strong {
      color: $primary-color;
    }
    
    a {
      color: $primary-color;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}
</style>
