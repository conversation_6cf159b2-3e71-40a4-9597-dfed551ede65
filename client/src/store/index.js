import Vue from 'vue'
import Vuex from 'vuex'
import api from '@/api'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    // 章节数据
    chapters: [],
    
    // 统计数据
    stats: {
      total: 0,
      chapters: []
    },
    
    // 当前选中的章节
    currentChapter: null,
    
    // 搜索历史
    searchHistory: JSON.parse(localStorage.getItem('searchHistory') || '[]'),
    
    // 收藏的妖怪
    favorites: JSON.parse(localStorage.getItem('favorites') || '[]'),
    
    // 加载状态
    loading: {
      chapters: false,
      stats: false,
      monsters: false
    }
  },
  
  getters: {
    // 获取章节列表
    chapterList: state => state.chapters,
    
    // 根据代码获取章节
    getChapterByCode: state => code => {
      return state.chapters.find(chapter => chapter.code === code)
    },
    
    // 获取统计信息
    statsData: state => state.stats,
    
    // 获取搜索历史
    getSearchHistory: state => state.searchHistory.slice(0, 10), // 最多显示10条
    
    // 检查是否收藏
    isFavorite: state => monsterId => {
      return state.favorites.includes(monsterId)
    },
    
    // 获取收藏数量
    favoritesCount: state => state.favorites.length,
    
    // 获取加载状态
    isLoading: state => key => state.loading[key] || false
  },
  
  mutations: {
    // 设置章节数据
    SET_CHAPTERS(state, chapters) {
      state.chapters = chapters
    },
    
    // 设置统计数据
    SET_STATS(state, stats) {
      state.stats = stats
    },
    
    // 设置当前章节
    SET_CURRENT_CHAPTER(state, chapter) {
      state.currentChapter = chapter
    },
    
    // 添加搜索历史
    ADD_SEARCH_HISTORY(state, keyword) {
      if (!keyword || keyword.trim() === '') return
      
      // 移除重复项
      const index = state.searchHistory.indexOf(keyword)
      if (index > -1) {
        state.searchHistory.splice(index, 1)
      }
      
      // 添加到开头
      state.searchHistory.unshift(keyword)
      
      // 限制数量
      if (state.searchHistory.length > 20) {
        state.searchHistory = state.searchHistory.slice(0, 20)
      }
      
      // 保存到本地存储
      localStorage.setItem('searchHistory', JSON.stringify(state.searchHistory))
    },
    
    // 清空搜索历史
    CLEAR_SEARCH_HISTORY(state) {
      state.searchHistory = []
      localStorage.removeItem('searchHistory')
    },
    
    // 添加收藏
    ADD_FAVORITE(state, monsterId) {
      if (!state.favorites.includes(monsterId)) {
        state.favorites.push(monsterId)
        localStorage.setItem('favorites', JSON.stringify(state.favorites))
      }
    },
    
    // 移除收藏
    REMOVE_FAVORITE(state, monsterId) {
      const index = state.favorites.indexOf(monsterId)
      if (index > -1) {
        state.favorites.splice(index, 1)
        localStorage.setItem('favorites', JSON.stringify(state.favorites))
      }
    },
    
    // 清空收藏
    CLEAR_FAVORITES(state) {
      state.favorites = []
      localStorage.removeItem('favorites')
    },
    
    // 设置加载状态
    SET_LOADING(state, { key, value }) {
      Vue.set(state.loading, key, value)
    }
  },
  
  actions: {
    // 加载章节数据
    async loadChapters({ commit, state }) {
      if (state.chapters.length > 0) return state.chapters
      
      commit('SET_LOADING', { key: 'chapters', value: true })
      
      try {
        const chapters = await api.chapters.getAll()
        commit('SET_CHAPTERS', chapters)
        return chapters
      } catch (error) {
        console.error('加载章节失败:', error)
        throw error
      } finally {
        commit('SET_LOADING', { key: 'chapters', value: false })
      }
    },
    
    // 加载统计数据
    async loadStats({ commit }) {
      commit('SET_LOADING', { key: 'stats', value: true })
      
      try {
        const stats = await api.stats.get()
        commit('SET_STATS', stats)
        return stats
      } catch (error) {
        console.error('加载统计数据失败:', error)
        throw error
      } finally {
        commit('SET_LOADING', { key: 'stats', value: false })
      }
    },
    
    // 搜索妖怪
    async searchMonsters({ commit }, keyword) {
      commit('ADD_SEARCH_HISTORY', keyword)
      
      try {
        const results = await api.monsters.search({ q: keyword })
        return results
      } catch (error) {
        console.error('搜索失败:', error)
        throw error
      }
    },
    
    // 切换收藏状态
    toggleFavorite({ commit, getters }, monsterId) {
      if (getters.isFavorite(monsterId)) {
        commit('REMOVE_FAVORITE', monsterId)
        return false
      } else {
        commit('ADD_FAVORITE', monsterId)
        return true
      }
    }
  }
})
