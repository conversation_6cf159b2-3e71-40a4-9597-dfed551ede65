<template>
  <div id="app">
    <!-- 导航栏 -->
    <AppHeader />
    
    <!-- 主要内容区域 -->
    <main class="main-content">
      <router-view />
    </main>
    
    <!-- 页脚 -->
    <AppFooter />
    
    <!-- 返回顶部按钮 -->
    <el-backtop :bottom="50" :right="50">
      <div class="back-top-btn">
        <i class="el-icon-caret-top"></i>
      </div>
    </el-backtop>
  </div>
</template>

<script>
import AppHeader from '@/components/layout/AppHeader.vue'
import AppFooter from '@/components/layout/AppFooter.vue'

export default {
  name: 'App',
  components: {
    AppHeader,
    AppFooter
  },
  
  created() {
    // 初始化应用数据
    this.initApp()
  },
  
  methods: {
    async initApp() {
      try {
        // 加载统计信息
        await this.$store.dispatch('loadStats')
        
        // 加载章节信息
        await this.$store.dispatch('loadChapters')
        
      } catch (error) {
        console.error('应用初始化失败:', error)
      }
    }
  }
}
</script>

<style lang="scss">
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: $bg-primary;
  color: $text-primary;
}

.main-content {
  flex: 1;
  padding-top: $header-height;
  min-height: calc(100vh - #{$header-height} - #{$footer-height});
}

// 返回顶部按钮样式
.back-top-btn {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
  }
}

// Element UI 主题定制
.el-button--primary {
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
  border: none;
  
  &:hover {
    background: linear-gradient(135deg, lighten($primary-color, 10%) 0%, lighten($secondary-color, 10%) 100%);
  }
}

.el-input__inner {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
  color: $text-primary;
  
  &:focus {
    border-color: $primary-color;
  }
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
}

.el-select-dropdown {
  background: $bg-secondary;
  border: 1px solid rgba(212, 175, 55, 0.3);
}

.el-select-dropdown__item {
  color: $text-primary;
  
  &:hover {
    background: rgba(212, 175, 55, 0.1);
  }
  
  &.selected {
    background: rgba(212, 175, 55, 0.2);
    color: $primary-color;
  }
}

.el-pagination {
  .el-pager li {
    background: rgba(255, 255, 255, 0.1);
    color: $text-primary;
    border: 1px solid rgba(212, 175, 55, 0.3);
    
    &:hover {
      color: $primary-color;
    }
    
    &.active {
      background: $primary-color;
      color: white;
    }
  }
  
  .btn-prev,
  .btn-next {
    background: rgba(255, 255, 255, 0.1);
    color: $text-primary;
    border: 1px solid rgba(212, 175, 55, 0.3);
    
    &:hover {
      color: $primary-color;
    }
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
  border-radius: 4px;
  
  &:hover {
    background: linear-gradient(135deg, lighten($primary-color, 10%) 0%, lighten($secondary-color, 10%) 100%);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .main-content {
    padding-top: 60px;
  }
}
</style>
