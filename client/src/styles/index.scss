// 全局样式文件
@import './variables.scss';

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: $font-family-primary;
  font-size: $font-size-md;
  line-height: $line-height-normal;
  color: $text-primary;
  background: $bg-primary;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 链接样式
a {
  color: $primary-color;
  text-decoration: none;
  transition: color $transition-fast;
  
  &:hover {
    color: $accent-color;
  }
}

// 标题样式
h1, h2, h3, h4, h5, h6 {
  font-family: $font-family-primary;
  font-weight: $font-weight-semibold;
  line-height: $line-height-tight;
  margin-bottom: $spacing-md;
  color: $text-gold;
}

h1 {
  font-size: $font-size-title;
  text-shadow: $text-glow;
}

h2 {
  font-size: $font-size-xxl;
}

h3 {
  font-size: $font-size-xl;
}

h4 {
  font-size: $font-size-lg;
}

// 段落样式
p {
  margin-bottom: $spacing-md;
  line-height: $line-height-loose;
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
  border-radius: $border-radius-md;
}

// 容器样式
.container {
  max-width: $container-max-width;
  margin: 0 auto;
  padding: 0 $spacing-lg;
  
  @media (max-width: $breakpoint-sm) {
    padding: 0 $spacing-md;
  }
}

// 卡片样式
.card {
  background: $bg-card;
  border-radius: $card-border-radius;
  padding: $card-padding;
  box-shadow: $card-shadow;
  border: 1px solid $border-primary;
  transition: all $transition-normal;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-heavy;
    border-color: $border-hover;
  }
}

// 按钮样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: $button-height;
  padding: $button-padding;
  border: none;
  border-radius: $button-border-radius;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  cursor: pointer;
  transition: all $transition-fast;
  text-decoration: none;
  
  &.btn-primary {
    background: $gradient-primary;
    color: white;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: $shadow-gold;
    }
  }
  
  &.btn-secondary {
    background: transparent;
    color: $primary-color;
    border: 1px solid $primary-color;
    
    &:hover {
      background: $primary-color;
      color: white;
    }
  }
  
  &.btn-ghost {
    background: transparent;
    color: $text-primary;
    border: 1px solid $border-primary;
    
    &:hover {
      border-color: $primary-color;
      color: $primary-color;
    }
  }
}

// 输入框样式
.input {
  width: 100%;
  height: 40px;
  padding: 0 $spacing-md;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid $border-primary;
  border-radius: $border-radius-md;
  color: $text-primary;
  font-size: $font-size-md;
  transition: all $transition-fast;
  
  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
  }
  
  &::placeholder {
    color: $text-muted;
  }
}

// 标签样式
.tag {
  display: inline-block;
  padding: $spacing-xs $spacing-sm;
  background: rgba(212, 175, 55, 0.2);
  color: $primary-color;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
}

// 分割线
.divider {
  height: 1px;
  background: $border-secondary;
  margin: $spacing-lg 0;
}

// 加载动画
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(212, 175, 55, 0.3);
    border-top: 3px solid $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 空状态
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xxl;
  color: $text-muted;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: $spacing-lg;
    opacity: 0.5;
  }
  
  .empty-text {
    font-size: $font-size-lg;
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: $text-primary; }
.text-secondary { color: $text-secondary; }
.text-muted { color: $text-muted; }
.text-gold { color: $text-gold; }

.bg-primary { background: $bg-primary; }
.bg-secondary { background: $bg-secondary; }
.bg-card { background: $bg-card; }

.mb-0 { margin-bottom: 0; }
.mb-sm { margin-bottom: $spacing-sm; }
.mb-md { margin-bottom: $spacing-md; }
.mb-lg { margin-bottom: $spacing-lg; }
.mb-xl { margin-bottom: $spacing-xl; }

.mt-0 { margin-top: 0; }
.mt-sm { margin-top: $spacing-sm; }
.mt-md { margin-top: $spacing-md; }
.mt-lg { margin-top: $spacing-lg; }
.mt-xl { margin-top: $spacing-xl; }

.p-0 { padding: 0; }
.p-sm { padding: $spacing-sm; }
.p-md { padding: $spacing-md; }
.p-lg { padding: $spacing-lg; }
.p-xl { padding: $spacing-xl; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-none { display: none; }

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

// 响应式工具类
@media (max-width: $breakpoint-sm) {
  .hidden-mobile { display: none !important; }
}

@media (min-width: $breakpoint-sm) {
  .hidden-desktop { display: none !important; }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity $transition-normal;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all $transition-normal;
}

.slide-up-enter,
.slide-up-leave-to {
  transform: translateY(20px);
  opacity: 0;
}
