// 黑神话悟空主题色彩变量

// 主色调 - 金色系
$primary-color: #d4af37;        // 主金色
$secondary-color: #b8860b;      // 深金色
$accent-color: #ffd700;         // 亮金色

// 背景色 - 暗色系
$bg-primary: linear-gradient(135deg, #1a1a1a 0%, #2d1810 50%, #1a1a1a 100%);
$bg-secondary: #2a2a2a;
$bg-tertiary: #3a3a3a;
$bg-card: rgba(42, 42, 42, 0.9);
$bg-overlay: rgba(0, 0, 0, 0.8);

// 文字颜色
$text-primary: #f5f5f5;         // 主要文字
$text-secondary: #cccccc;       // 次要文字
$text-muted: #999999;           // 弱化文字
$text-gold: #d4af37;            // 金色文字

// 边框颜色
$border-primary: rgba(212, 175, 55, 0.3);
$border-secondary: rgba(255, 255, 255, 0.1);
$border-hover: rgba(212, 175, 55, 0.6);

// 阴影
$shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
$shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.2);
$shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.3);
$shadow-gold: 0 0 20px rgba(212, 175, 55, 0.3);

// 渐变
$gradient-primary: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
$gradient-secondary: linear-gradient(135deg, #2d1810 0%, #1a1a1a 100%);
$gradient-card: linear-gradient(135deg, rgba(42, 42, 42, 0.9) 0%, rgba(26, 26, 26, 0.9) 100%);

// 尺寸变量
$header-height: 80px;
$footer-height: 120px;
$sidebar-width: 280px;
$container-max-width: 1200px;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// 圆角
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;

// 字体
$font-family-primary: 'Noto Serif SC', serif;
$font-family-secondary: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;
$font-size-title: 32px;

// 字重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.2;
$line-height-normal: 1.5;
$line-height-loose: 1.8;

// 过渡动画
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

// Z-index层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 断点
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1600px;

// 状态颜色
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #f5222d;
$info-color: #1890ff;

// 特殊效果
$glow-effect: 0 0 20px rgba(212, 175, 55, 0.5);
$text-glow: 0 0 10px rgba(212, 175, 55, 0.8);

// 卡片样式
$card-padding: $spacing-lg;
$card-border-radius: $border-radius-lg;
$card-shadow: $shadow-medium;

// 按钮样式
$button-height: 40px;
$button-padding: 0 $spacing-lg;
$button-border-radius: $border-radius-md;
