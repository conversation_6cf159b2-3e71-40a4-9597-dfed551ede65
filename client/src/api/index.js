import axios from 'axios'

/**
 * API服务配置
 */

// 创建axios实例
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 可以在这里添加token等认证信息
    console.log('API请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    const { data } = response
    
    // 统一处理响应数据
    if (data.success) {
      return data.data
    } else {
      throw new Error(data.error || '请求失败')
    }
  },
  error => {
    console.error('响应错误:', error)
    
    // 统一错误处理
    let message = '网络错误'
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          message = data.error || '请求参数错误'
          break
        case 401:
          message = '未授权访问'
          break
        case 403:
          message = '禁止访问'
          break
        case 404:
          message = '资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data.error || `请求失败 (${status})`
      }
    } else if (error.request) {
      message = '网络连接失败'
    }
    
    return Promise.reject(new Error(message))
  }
)

/**
 * API接口定义
 */
export default {
  // 章节相关
  chapters: {
    // 获取所有章节
    getAll() {
      return api.get('/chapters')
    }
  },
  
  // 妖怪相关
  monsters: {
    // 获取妖怪列表
    getList(params = {}) {
      return api.get('/monsters', { params })
    },
    
    // 获取妖怪详情
    getById(id) {
      return api.get(`/monsters/${id}`)
    },
    
    // 搜索妖怪
    search(params = {}) {
      return api.get('/search', { params })
    },
    
    // 获取随机妖怪
    getRandom() {
      return api.get('/random')
    }
  },
  
  // 统计相关
  stats: {
    // 获取统计信息
    get() {
      return api.get('/stats')
    }
  }
}
