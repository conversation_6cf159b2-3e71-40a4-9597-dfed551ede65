import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// Element UI
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

// 自定义样式
import './styles/index.scss'

// API服务
import api from './api'

// 全局配置
Vue.config.productionTip = false

// 使用Element UI
Vue.use(ElementUI, {
  size: 'medium'
})

// 全局注册API服务
Vue.prototype.$api = api

// 全局过滤器
Vue.filter('truncate', function (text, length = 100) {
  if (!text) return ''
  if (text.length <= length) return text
  return text.substring(0, length) + '...'
})

// 全局混入
Vue.mixin({
  methods: {
    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString('zh-CN')
    },
    
    // 获取图片URL
    getImageUrl(imagePath) {
      if (!imagePath) return ''
      // 如果是完整URL，直接返回
      if (imagePath.startsWith('http')) return imagePath
      // 否则拼接本地路径
      return `/images/${imagePath}`
    },
    
    // 错误处理
    handleError(error, defaultMessage = '操作失败') {
      console.error(error)
      const message = (error.response && error.response.data && error.response.data.error) || error.message || defaultMessage
      this.$message.error(message)
    },
    
    // 成功提示
    showSuccess(message) {
      this.$message.success(message)
    }
  }
})

// 创建Vue实例
new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
