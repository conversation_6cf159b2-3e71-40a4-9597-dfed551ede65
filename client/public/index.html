<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title>寻找天命人 - 黑神话悟空妖怪平生录</title>
    <meta name="description" content="《黑神话：悟空》妖怪平生录，203个妖怪，203首小诗，203个妖生故事，带你去看妖怪的众生相">
    <meta name="keywords" content="黑神话悟空,妖怪,平生录,天命人,游戏">
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
      /* 加载动画 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #1a1a1a 0%, #2d1810 50%, #1a1a1a 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        font-family: 'Noto Serif SC', serif;
      }
      
      .loading-title {
        color: #d4af37;
        font-size: 2.5rem;
        font-weight: 600;
        margin-bottom: 2rem;
        text-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
        animation: glow 2s ease-in-out infinite alternate;
      }
      
      .loading-spinner {
        width: 60px;
        height: 60px;
        border: 3px solid rgba(212, 175, 55, 0.3);
        border-top: 3px solid #d4af37;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-text {
        color: #8b7355;
        font-size: 1rem;
        margin-top: 1rem;
        animation: pulse 1.5s ease-in-out infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      @keyframes glow {
        from { text-shadow: 0 0 20px rgba(212, 175, 55, 0.5); }
        to { text-shadow: 0 0 30px rgba(212, 175, 55, 0.8), 0 0 40px rgba(212, 175, 55, 0.6); }
      }
      
      @keyframes pulse {
        0%, 100% { opacity: 0.6; }
        50% { opacity: 1; }
      }
      
      /* 隐藏加载动画 */
      .loading-container.hidden {
        opacity: 0;
        transition: opacity 0.5s ease-out;
        pointer-events: none;
      }
    </style>
  </head>
  <body>
    <noscript>
      <strong>很抱歉，寻找天命人需要启用JavaScript才能正常运行。</strong>
    </noscript>
    
    <!-- 加载动画 -->
    <div id="loading" class="loading-container">
      <div class="loading-title">寻找天命人</div>
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载妖怪平生录...</div>
    </div>
    
    <div id="app"></div>
    
    <script>
      // 页面加载完成后隐藏加载动画
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.classList.add('hidden');
            setTimeout(function() {
              loading.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
