# 《黑神话：悟空》妖怪平生录 - 技术架构详解

## 项目概述

本项目是一个全栈Web应用，将《黑神话：悟空》的妖怪平生录从Markdown文档转换为现代化的数字展示平台。作为后端开发者，本文档将详细解析项目中的后端技术架构、数据处理流程和关键脚本的使用。

## 技术栈架构

### 后端技术栈
- **Node.js**: JavaScript运行环境
- **Express.js**: Web应用框架
- **SQLite**: 轻量级关系型数据库
- **fs/path**: Node.js文件系统操作
- **concurrently**: 并发运行多个命令的工具

### 前端技术栈
- **Vue.js 2.6**: 渐进式JavaScript框架
- **Element UI**: 基于Vue的组件库
- **Vue Router**: 前端路由管理
- **Vuex**: 状态管理
- **Axios**: HTTP客户端

## 项目目录结构

```
project_demo/
├── server/                 # 后端服务
│   ├── app.js             # Express应用主文件
│   └── database/          # 数据库文件目录
├── scripts/               # 数据处理脚本
│   ├── parseMarkdown.js   # Markdown解析脚本
│   └── initDatabase.js    # 数据库初始化脚本
├── client/                # Vue前端项目
├── data/                  # JSON数据文件
├── 黑神话悟空妖怪平生录/    # 原始资源文件
└── package.json          # 项目配置文件
```

## 核心后端技术详解

### 1. Express.js 应用架构

#### 1.1 应用主文件 (server/app.js)

```javascript
const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const cors = require('cors');

class MonsterRecordsServer {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 3000;
        this.db = null;
        
        this.initializeMiddleware();
        this.initializeDatabase();
        this.initializeRoutes();
    }
}
```

**关键技术点：**
- **类封装设计**: 使用ES6类封装整个服务器应用
- **中间件模式**: Express的中间件机制处理请求
- **数据库连接池**: SQLite连接管理

#### 1.2 中间件配置

```javascript
initializeMiddleware() {
    // CORS跨域处理
    this.app.use(cors({
        origin: ['http://localhost:8080', 'http://127.0.0.1:8080'],
        credentials: true
    }));
    
    // JSON解析中间件
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    
    // 静态文件服务
    this.app.use('/images', express.static(path.join(__dirname, '..')));
}
```

**学习要点：**
- **CORS配置**: 解决前后端分离的跨域问题
- **请求体解析**: 处理JSON和表单数据
- **静态文件服务**: 提供图片等静态资源访问

### 2. SQLite 数据库设计

#### 2.1 数据库表结构

```sql
CREATE TABLE IF NOT EXISTS monsters (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    chapter TEXT NOT NULL,
    chapter_code TEXT NOT NULL,
    chapter_num INTEGER NOT NULL,
    monster_num INTEGER NOT NULL,
    poetry TEXT,
    story TEXT,
    image TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**设计考虑：**
- **主键设计**: 使用TEXT类型的ID，便于前端处理
- **章节管理**: chapter_code用于路由，chapter_num用于排序
- **索引优化**: 为搜索和排序字段创建索引

#### 2.2 索引优化策略

```sql
-- 基础索引
CREATE INDEX IF NOT EXISTS idx_monsters_chapter ON monsters(chapter_code);
CREATE INDEX IF NOT EXISTS idx_monsters_name ON monsters(name);
CREATE INDEX IF NOT EXISTS idx_monsters_chapter_num ON monsters(chapter_num, monster_num);

-- 搜索优化索引
CREATE INDEX IF NOT EXISTS idx_monsters_name_search ON monsters(name COLLATE NOCASE);
```

**性能优化要点：**
- **复合索引**: chapter_num + monster_num 提升排序性能
- **搜索索引**: COLLATE NOCASE 支持大小写不敏感搜索
- **选择性索引**: 只为高频查询字段创建索引

### 3. RESTful API 设计

#### 3.1 API 路由结构

```javascript
// 统计信息API
this.app.get('/api/stats', this.getStats.bind(this));

// 章节相关API
this.app.get('/api/chapters', this.getChapters.bind(this));

// 妖怪相关API
this.app.get('/api/monsters', this.getMonsters.bind(this));
this.app.get('/api/monsters/:id', this.getMonsterById.bind(this));
this.app.get('/api/monsters/random', this.getRandomMonster.bind(this));

// 搜索API
this.app.get('/api/search', this.searchMonsters.bind(this));
```

**RESTful设计原则：**
- **资源导向**: URL表示资源，HTTP方法表示操作
- **统一接口**: 一致的请求/响应格式
- **无状态**: 每个请求包含完整信息

#### 3.2 分页查询实现

```javascript
getMonsters(req, res) {
    const { 
        page = 1, 
        pageSize = 12, 
        chapter, 
        name, 
        sortBy = 'chapter_num' 
    } = req.query;
    
    const offset = (parseInt(page) - 1) * parseInt(pageSize);
    const limit = parseInt(pageSize);
    
    // 构建WHERE条件
    let whereConditions = ['1=1'];
    let params = [];
    
    if (chapter) {
        whereConditions.push('chapter_code = ?');
        params.push(chapter);
    }
    
    if (name) {
        whereConditions.push('name LIKE ?');
        params.push(`%${name}%`);
    }
    
    // 执行查询...
}
```

**分页技术要点：**
- **OFFSET/LIMIT**: SQLite分页查询语法
- **动态WHERE**: 根据查询参数构建条件
- **参数绑定**: 防止SQL注入攻击

### 4. 搜索功能优化

#### 4.1 高性能搜索实现

```javascript
searchMonsters(req, res) {
    const { q, limit = 20 } = req.query;
    
    // 优化的搜索SQL - 只搜索名称字段
    const sql = `
        SELECT *,
            CASE 
                WHEN name = ? THEN 1          -- 精确匹配
                WHEN name LIKE ? THEN 2       -- 开头匹配
                WHEN name LIKE ? THEN 3       -- 包含匹配
                ELSE 4
            END as priority
        FROM monsters 
        WHERE (name = ? OR name LIKE ? OR name LIKE ?)
        ORDER BY priority, chapter_num, monster_num
        LIMIT ?
    `;
    
    const searchTerm = `%${keyword}%`;
    const startTerm = `${keyword}%`;
    
    const params = [
        keyword, startTerm, searchTerm,  // CASE WHEN 参数
        keyword, startTerm, searchTerm,  // WHERE 条件参数
        searchLimit                      // LIMIT 参数
    ];
}
```

**搜索优化策略：**
- **优先级排序**: CASE WHEN 实现搜索结果优先级
- **单字段搜索**: 只搜索名称，提升性能
- **参数化查询**: 防止SQL注入，提高安全性

## 数据处理脚本详解

### 1. Markdown 解析脚本 (scripts/parseMarkdown.js)

#### 1.1 文件解析流程

```javascript
class MarkdownParser {
    constructor() {
        this.sourceDir = path.join(__dirname, '../黑神话悟空妖怪平生录');
        this.outputDir = path.join(__dirname, '../data');
        this.chapters = [
            { name: '小妖', code: 'xiaoyao', num: 1 },
            { name: '头目', code: 'toumu', num: 2 },
            { name: '妖王', code: 'yaowang', num: 3 },
            { name: '人物', code: 'renwu', num: 4 }
        ];
    }
    
    async parseAllChapters() {
        const allMonsters = [];
        
        for (const chapter of this.chapters) {
            console.log(`解析章节: ${chapter.name}`);
            const monsters = await this.parseChapter(chapter);
            allMonsters.push(...monsters);
        }
        
        return allMonsters;
    }
}
```

**解析技术要点：**
- **异步处理**: async/await 处理文件I/O操作
- **批量处理**: 循环处理多个章节文件
- **错误处理**: try/catch 捕获解析异常

#### 1.2 正则表达式解析

```javascript
parseMonsterContent(content) {
    // 解析妖怪名称
    const nameMatch = content.match(/^##\s*(.+)$/m);
    const name = nameMatch ? nameMatch[1].trim() : '';
    
    // 解析诗词内容
    const poetryMatch = content.match(/```\n([\s\S]*?)\n```/);
    const poetry = poetryMatch ? poetryMatch[1].trim() : '';
    
    // 解析故事内容
    const storyMatch = content.match(/```\n[\s\S]*?\n```\n\n([\s\S]*?)(?=\n##|\n$|$)/);
    const story = storyMatch ? storyMatch[1].trim() : '';
    
    return { name, poetry, story };
}
```

**正则表达式技巧：**
- **多行匹配**: `m` 标志处理多行文本
- **贪婪/非贪婪**: `*?` 非贪婪匹配避免过度匹配
- **分组捕获**: `()` 捕获需要的内容部分

### 2. 数据库初始化脚本 (scripts/initDatabase.js)

#### 2.1 数据库初始化流程

```javascript
class DatabaseInitializer {
    constructor() {
        this.dbPath = path.join(__dirname, '../server/database/monsters.db');
        this.dataPath = path.join(__dirname, '../data/monsters.json');
        this.db = null;
    }
    
    async initialize() {
        try {
            await this.ensureDirectoryExists();
            await this.connectDatabase();
            await this.createTables();
            await this.loadData();
            console.log('数据库初始化完成');
        } catch (error) {
            console.error('数据库初始化失败:', error);
            throw error;
        } finally {
            if (this.db) {
                this.db.close();
            }
        }
    }
}
```

**初始化技术要点：**
- **目录检查**: 确保数据库目录存在
- **事务处理**: 批量插入数据使用事务
- **资源管理**: finally 块确保数据库连接关闭

#### 2.2 批量数据插入优化

```javascript
async loadData() {
    const monsters = JSON.parse(fs.readFileSync(this.dataPath, 'utf8'));
    
    return new Promise((resolve, reject) => {
        this.db.serialize(() => {
            // 开始事务
            this.db.run('BEGIN TRANSACTION');
            
            const stmt = this.db.prepare(`
                INSERT OR REPLACE INTO monsters 
                (id, name, chapter, chapter_code, chapter_num, monster_num, poetry, story, image) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);
            
            // 批量插入
            monsters.forEach(monster => {
                stmt.run([
                    monster.id, monster.name, monster.chapter,
                    monster.chapter_code, monster.chapter_num, monster.monster_num,
                    monster.poetry, monster.story, monster.image
                ]);
            });
            
            stmt.finalize();
            
            // 提交事务
            this.db.run('COMMIT', (err) => {
                if (err) reject(err);
                else resolve();
            });
        });
    });
}
```

**批量插入优化：**
- **事务处理**: BEGIN/COMMIT 提升插入性能
- **预编译语句**: prepare() 避免重复编译SQL
- **批量操作**: 一次事务处理所有数据

## NPM 脚本配置

### package.json 脚本详解

```json
{
  "scripts": {
    "dev": "concurrently \"npm run server\" \"npm run client\"",
    "server": "nodemon server/app.js",
    "client": "cd client && npm run serve",
    "parse-data": "node scripts/parseMarkdown.js",
    "init-db": "node scripts/initDatabase.js",
    "build": "cd client && npm run build",
    "start": "node server/app.js"
  }
}
```

**脚本使用说明：**

1. **开发环境启动**:
   ```bash
   npm run dev
   ```
   - 并发启动前后端服务
   - 使用 concurrently 工具同时运行多个命令

2. **数据解析**:
   ```bash
   npm run parse-data
   ```
   - 解析 Markdown 文件生成 JSON 数据
   - 处理图片路径和内容格式化

3. **数据库初始化**:
   ```bash
   npm run init-db
   ```
   - 创建数据库表结构
   - 导入解析后的 JSON 数据

4. **生产环境部署**:
   ```bash
   npm run build
   npm start
   ```
   - 构建前端静态文件
   - 启动生产环境服务器

## 开发工具配置

### 1. nodemon 配置

```json
{
  "watch": ["server/"],
  "ext": "js,json",
  "ignore": ["server/database/"],
  "exec": "node server/app.js"
}
```

### 2. 环境变量配置

```javascript
const config = {
    port: process.env.PORT || 3000,
    dbPath: process.env.DB_PATH || './server/database/monsters.db',
    staticPath: process.env.STATIC_PATH || '../黑神话悟空妖怪平生录'
};
```

## 性能优化策略

### 1. 数据库优化
- **索引策略**: 为高频查询字段创建索引
- **查询优化**: 使用 LIMIT 限制结果集大小
- **连接池**: 复用数据库连接

### 2. API 优化
- **分页查询**: 避免一次性加载大量数据
- **缓存策略**: 静态数据可考虑内存缓存
- **压缩响应**: gzip 压缩减少传输大小

### 3. 搜索优化
- **索引优化**: 为搜索字段创建专门索引
- **查询简化**: 减少搜索字段提升性能
- **结果限制**: 限制搜索结果数量

## 错误处理和日志

### 1. 统一错误处理

```javascript
// 全局错误处理中间件
this.app.use((err, req, res, next) => {
    console.error('服务器错误:', err);
    res.status(500).json({
        success: false,
        error: '服务器内部错误'
    });
});
```

### 2. 请求日志

```javascript
// 请求日志中间件
this.app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    next();
});
```

## 部署考虑

### 1. 生产环境配置
- **环境变量**: 使用环境变量管理配置
- **进程管理**: PM2 管理 Node.js 进程
- **反向代理**: Nginx 处理静态文件和负载均衡

### 2. 数据备份
- **定期备份**: 定时备份 SQLite 数据库文件
- **版本控制**: 数据结构变更的版本管理

## 学习建议

### 1. 后端技能提升
- **深入学习 Express.js**: 中间件机制、路由设计
- **数据库优化**: SQL 查询优化、索引设计
- **API 设计**: RESTful 规范、接口文档

### 2. 实践项目
- **扩展功能**: 添加用户系统、评论功能
- **性能监控**: 集成性能监控工具
- **测试覆盖**: 编写单元测试和集成测试

## 总结

这个项目展示了一个完整的后端开发流程，从数据处理到API设计，再到性能优化，涵盖了后端开发的核心技能：

### 核心技术收获
1. **Express.js 应用架构**: 学会构建可扩展的Web服务
2. **SQLite 数据库设计**: 掌握关系型数据库的设计和优化
3. **RESTful API 开发**: 理解标准的API设计规范
4. **数据处理脚本**: 学会编写自动化数据处理工具
5. **性能优化策略**: 掌握数据库和API的性能优化方法

### 实际应用价值
- **完整的开发流程**: 从需求分析到部署上线的完整经验
- **实用的技术栈**: 企业级开发中广泛使用的技术组合
- **可扩展的架构**: 为后续功能扩展打下良好基础

通过学习和实践这些技术，可以建立扎实的后端开发基础，为进一步的技术成长奠定坚实基础。
