# 《黑神话：悟空》妖怪平生录 - 开发问题与解决方案

## 项目概述

本文档记录了在开发《黑神话：悟空》妖怪平生录项目过程中遇到的各种技术问题及其解决方案。这些问题涵盖了前端、后端、数据处理、性能优化等多个方面，希望能为其他开发者提供参考。

## 目录

1. [项目初始化问题](#项目初始化问题)
2. [数据处理问题](#数据处理问题)
3. [前端构建问题](#前端构建问题)
4. [后端API问题](#后端API问题)
5. [数据库设计问题](#数据库设计问题)
6. [性能优化问题](#性能优化问题)
7. [图片资源问题](#图片资源问题)
8. [搜索功能问题](#搜索功能问题)

## 项目初始化问题

### 问题1: Vue CLI版本兼容性问题

**问题描述:**
```bash
'vue-cli-service' 不是内部或外部命令，也不是可运行的程序
```

**问题原因:**
- Vue CLI版本不匹配
- package.json中的依赖版本过高
- 缺少必要的开发依赖

**解决方案:**
```json
// 修改 client/package.json
{
  "devDependencies": {
    "@vue/cli-service": "^4.5.19",  // 降低版本
    "sass": "^1.32.13",             // 兼容版本
    "sass-loader": "^10.2.1",       // 兼容版本
    "vue-template-compiler": "^2.6.14"
  }
}
```

**学习要点:**
- 依赖版本兼容性很重要
- 使用稳定版本避免兼容性问题
- package-lock.json 锁定依赖版本

### 问题2: Vue配置文件语法错误

**问题描述:**
```bash
TypeError: defineConfig is not a function
```

**问题原因:**
- Vue CLI 4.x 不支持 defineConfig
- 配置文件语法不匹配版本

**解决方案:**
```javascript
// vue.config.js - 修改前
const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  // ...
})

// vue.config.js - 修改后
module.exports = {
  transpileDependencies: [],
  // ...
}
```

**学习要点:**
- 不同版本的API差异
- 配置文件要匹配对应版本
- 查阅官方文档确认语法

## 数据处理问题

### 问题3: Markdown文件编码问题

**问题描述:**
```bash
Error: ENOENT: no such file or directory, open '黑神话悟空妖怪平生录\小妖.md'
```

**问题原因:**
- Windows路径分隔符问题
- 中文文件名编码问题
- 相对路径解析错误

**解决方案:**
```javascript
// scripts/parseMarkdown.js
const path = require('path');

// 错误的路径处理
const filePath = `${this.sourceDir}\\${chapter.name}.md`;

// 正确的路径处理
const filePath = path.join(this.sourceDir, `${chapter.name}.md`);

// 确保目录存在
if (!fs.existsSync(this.sourceDir)) {
    throw new Error(`源目录不存在: ${this.sourceDir}`);
}
```

**学习要点:**
- 使用 path.join() 处理跨平台路径
- 处理中文文件名要注意编码
- 添加文件存在性检查

### 问题4: 正则表达式解析不准确

**问题描述:**
- 诗词内容解析不完整
- 故事内容包含多余的格式字符
- 妖怪名称提取错误

**问题原因:**
- 正则表达式贪婪匹配问题
- 多行文本处理不当
- 边界条件考虑不足

**解决方案:**
```javascript
// 改进的正则表达式
parseMonsterContent(content) {
    // 妖怪名称 - 更精确的匹配
    const nameMatch = content.match(/^##\s*(.+?)(?:\s*\{[^}]*\})?\s*$/m);
    const name = nameMatch ? nameMatch[1].trim() : '';
    
    // 诗词内容 - 非贪婪匹配
    const poetryMatch = content.match(/```\n([\s\S]*?)\n```/);
    const poetry = poetryMatch ? poetryMatch[1].trim() : '';
    
    // 故事内容 - 更精确的边界
    const storyMatch = content.match(/```\n[\s\S]*?\n```\n\n([\s\S]*?)(?=\n##|\n$|$)/);
    let story = storyMatch ? storyMatch[1].trim() : '';
    
    // 清理故事内容中的多余空行
    story = story.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    return { name, poetry, story };
}
```

**学习要点:**
- 非贪婪匹配 `*?` 的重要性
- 多行模式 `m` 标志的使用
- 边界条件的处理

### 问题5: 图片路径处理问题

**问题描述:**
- 图片路径在不同操作系统下不一致
- 图片文件名包含特殊字符
- 路径拼接错误导致404

**解决方案:**
```javascript
// 图片路径标准化处理
normalizeImagePath(imagePath) {
    if (!imagePath) return null;
    
    // 统一使用正斜杠
    const normalizedPath = imagePath.replace(/\\/g, '/');
    
    // 确保路径以目录名开头
    if (!normalizedPath.startsWith('黑神话悟空妖怪平生录/')) {
        return `黑神话悟空妖怪平生录/${normalizedPath}`;
    }
    
    return normalizedPath;
}
```

**学习要点:**
- 路径标准化的重要性
- 跨平台兼容性考虑
- 路径拼接的最佳实践

## 前端构建问题

### 问题6: ES6语法兼容性问题

**问题描述:**
```bash
Module parse failed: Unexpected token (55:37)
error?.response?.data?.error
```

**问题原因:**
- 可选链操作符 `?.` 在旧版本webpack中不支持
- Babel配置不包含相应的转换插件

**解决方案:**
```javascript
// 修改前 - 使用可选链操作符
const message = error.response?.data?.error || error.message || defaultMessage;

// 修改后 - 兼容性写法
const message = (error.response && error.response.data && error.response.data.error) 
    || error.message 
    || defaultMessage;
```

**学习要点:**
- ES6+语法的兼容性问题
- Babel配置的重要性
- 向后兼容的编程习惯

### 问题7: SCSS导入路径问题

**问题描述:**
```bash
@import "@/styles/variables.scss" 路径解析失败
```

**解决方案:**
```scss
// 修改前
@import "@/styles/variables.scss";

// 修改后 - 使用相对路径
@import './variables.scss';
```

**学习要点:**
- 路径别名配置
- SCSS导入规则
- 构建工具的路径解析

## 后端API问题

### 问题8: CORS跨域问题

**问题描述:**
```bash
Access to XMLHttpRequest at 'http://localhost:3000/api/monsters' 
from origin 'http://localhost:8080' has been blocked by CORS policy
```

**解决方案:**
```javascript
// server/app.js
const cors = require('cors');

this.app.use(cors({
    origin: [
        'http://localhost:8080',
        'http://127.0.0.1:8080'
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
```

**学习要点:**
- CORS的工作原理
- 开发环境vs生产环境的配置差异
- 安全性考虑

### 问题9: 静态文件服务配置

**问题描述:**
- 图片资源404错误
- 静态文件路径映射不正确

**解决方案:**
```javascript
// 静态文件服务配置
this.app.use('/images', express.static(path.join(__dirname, '..')));

// 图片URL生成
getImageUrl(imagePath) {
    if (!imagePath) return '';
    if (imagePath.startsWith('http')) return imagePath;
    return `/images/${imagePath}`;
}
```

**学习要点:**
- Express静态文件中间件
- 路径映射的配置
- URL生成的统一处理

## 数据库设计问题

### 问题10: SQLite数据类型选择

**问题描述:**
- ID字段类型选择困惑
- 数字字段的精度问题
- 文本字段长度限制

**解决方案:**
```sql
CREATE TABLE IF NOT EXISTS monsters (
    id TEXT PRIMARY KEY,              -- 使用TEXT便于前端处理
    name TEXT NOT NULL,
    chapter TEXT NOT NULL,
    chapter_code TEXT NOT NULL,
    chapter_num INTEGER NOT NULL,     -- 使用INTEGER便于排序
    monster_num INTEGER NOT NULL,
    poetry TEXT,                      -- TEXT类型无长度限制
    story TEXT,
    image TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**学习要点:**
- SQLite数据类型的特点
- 主键设计的考虑因素
- 前后端数据类型匹配

### 问题11: 数据库索引优化

**问题描述:**
- 查询性能慢
- 搜索响应时间长
- 排序操作耗时

**解决方案:**
```sql
-- 基础索引
CREATE INDEX IF NOT EXISTS idx_monsters_chapter ON monsters(chapter_code);
CREATE INDEX IF NOT EXISTS idx_monsters_name ON monsters(name);

-- 复合索引用于排序
CREATE INDEX IF NOT EXISTS idx_monsters_chapter_num ON monsters(chapter_num, monster_num);

-- 搜索优化索引
CREATE INDEX IF NOT EXISTS idx_monsters_name_search ON monsters(name COLLATE NOCASE);
```

**学习要点:**
- 索引的创建策略
- 复合索引的使用场景
- 搜索性能优化

## 性能优化问题

### 问题12: 搜索性能问题

**问题描述:**
- 搜索响应时间超过5秒
- 复杂查询导致数据库压力大
- 前端搜索体验差

**第一次优化尝试:**
```javascript
// 问题：复杂的多步骤查询
const searches = [
    { sql: `SELECT *, 1 as priority FROM monsters WHERE name = ?` },
    { sql: `SELECT *, 2 as priority FROM monsters WHERE name LIKE ?` },
    { sql: `SELECT *, 3 as priority FROM monsters WHERE poetry LIKE ?` },
    { sql: `SELECT *, 4 as priority FROM monsters WHERE story LIKE ?` }
];

// 串行执行多个查询
for (const search of searches) {
    const results = await executeQuery(search);
    // 处理结果...
}
```

**最终解决方案:**
```javascript
// 优化：单个SQL查询
const sql = `
    SELECT *,
        CASE 
            WHEN name = ? THEN 1
            WHEN name LIKE ? THEN 2
            WHEN name LIKE ? THEN 3
            ELSE 4
        END as priority
    FROM monsters 
    WHERE (name = ? OR name LIKE ? OR name LIKE ?)
    ORDER BY priority, chapter_num, monster_num
    LIMIT ?
`;
```

**性能提升结果:**
- 搜索时间从5秒降低到1-5毫秒
- 数据库查询次数从多次减少到1次
- 用户体验显著改善

**学习要点:**
- SQL查询优化的重要性
- 避免N+1查询问题
- 单次查询vs多次查询的权衡

### 问题13: 前端搜索防抖

**问题描述:**
- 用户输入时频繁发送请求
- 服务器压力大
- 网络资源浪费

**解决方案:**
```javascript
// 防抖搜索实现
handleSearch(immediate = false) {
    const keyword = this.searchKeyword.trim();
    
    // 清除之前的定时器
    if (this.searchTimer) {
        clearTimeout(this.searchTimer);
    }
    
    // 防抖处理
    if (!immediate) {
        this.searchTimer = setTimeout(() => {
            this.performSearch(keyword);
        }, 500); // 500ms防抖
        return;
    }
    
    this.performSearch(keyword);
}
```

**学习要点:**
- 防抖技术的应用
- 用户体验优化
- 网络请求优化

## 图片资源问题

### 问题14: 图片路径URL编码问题

**问题描述:**
```bash
GET /images/黑神话悟空妖怪平生录/image-20240820-203558.png 404
```

**问题原因:**
- 中文路径被URL编码
- 静态文件服务路径映射错误
- 图片文件实际路径不匹配

**解决方案:**
```javascript
// 后端静态文件服务配置
this.app.use('/images', express.static(path.join(__dirname, '..')));

// 前端图片URL处理
getImageUrl(imagePath) {
    if (!imagePath) return '';
    if (imagePath.startsWith('http')) return imagePath;
    // 图片路径已经包含了目录名，直接拼接
    return `/images/${imagePath}`;
}
```

**学习要点:**
- 中文路径的处理
- 静态资源服务配置
- URL编码问题的解决

## 搜索功能问题

### 问题15: 搜索需求变更

**问题描述:**
- 初始设计支持全文搜索（名称、诗词、故事）
- 用户反馈只需要按名称搜索
- 需要简化搜索逻辑

**解决方案:**
```javascript
// 简化后的搜索SQL
const sql = `
    SELECT *,
        CASE 
            WHEN name = ? THEN 1          -- 精确匹配
            WHEN name LIKE ? THEN 2       -- 开头匹配
            WHEN name LIKE ? THEN 3       -- 包含匹配
            ELSE 4
        END as priority
    FROM monsters 
    WHERE (name = ? OR name LIKE ? OR name LIKE ?)
    ORDER BY priority, chapter_num, monster_num
    LIMIT ?
`;
```

**前端界面调整:**
```javascript
// 更新搜索提示文本
placeholder="输入妖怪名称进行搜索..."

// 移除诗词和故事的高亮显示
<p class="poetry-text">{{ monster.poetry }}</p>
<p class="story-text">{{ monster.story | truncate(150) }}</p>
```

**学习要点:**
- 需求变更的处理
- 代码重构的技巧
- 用户体验的优化

## 开发环境问题

### 问题16: 并发启动问题

**问题描述:**
- 前后端需要分别启动
- 开发效率低
- 端口冲突问题

**解决方案:**
```json
// package.json
{
  "scripts": {
    "dev": "concurrently \"npm run server\" \"npm run client\"",
    "server": "nodemon server/app.js",
    "client": "cd client && npm run serve"
  },
  "devDependencies": {
    "concurrently": "^7.6.0",
    "nodemon": "^2.0.20"
  }
}
```

**学习要点:**
- 开发工具的选择
- 自动化开发流程
- 提高开发效率

## 总结与经验

### 主要问题类型

1. **环境配置问题** (40%)
   - 依赖版本兼容性
   - 构建工具配置
   - 开发环境搭建

2. **数据处理问题** (25%)
   - 文件解析
   - 数据格式转换
   - 路径处理

3. **性能优化问题** (20%)
   - 数据库查询优化
   - 前端交互优化
   - 网络请求优化

4. **功能实现问题** (15%)
   - API设计
   - 前后端交互
   - 用户体验优化

### 解决问题的方法论

1. **问题定位**
   - 查看错误日志
   - 使用调试工具
   - 分析问题根因

2. **解决方案研究**
   - 查阅官方文档
   - 搜索相关问题
   - 分析最佳实践

3. **方案实施**
   - 小步快跑
   - 逐步验证
   - 记录变更

4. **效果验证**
   - 功能测试
   - 性能测试
   - 用户体验测试

### 预防措施

1. **技术选型**
   - 选择稳定版本
   - 考虑兼容性
   - 评估学习成本

2. **开发规范**
   - 统一代码风格
   - 完善错误处理
   - 添加必要注释

3. **测试策略**
   - 单元测试
   - 集成测试
   - 性能测试

4. **文档维护**
   - 及时更新文档
   - 记录重要决策
   - 分享经验教训

通过这个项目的开发，我们积累了宝贵的实战经验，这些问题和解决方案对于类似项目的开发具有重要的参考价值。
