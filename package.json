{"name": "black-myth-wukong-monster-records", "version": "1.0.0", "description": "《黑神话：悟空》妖怪平生录 - 寻找天命人", "main": "server/app.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "nodemon server/app.js", "client": "cd client && npm run serve", "build": "cd client && npm run build", "parse-data": "node scripts/parseMarkdown.js", "init-db": "node scripts/initDatabase.js", "setup": "npm run parse-data && npm run init-db"}, "keywords": ["黑神话悟空", "妖怪", "平生录", "vue", "element-ui", "sqlite"], "author": "寻找天命人项目组", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "sqlite3": "^5.1.6", "multer": "^1.4.5-lts.1", "path": "^0.12.7"}, "devDependencies": {"nodemon": "^3.0.1", "concurrently": "^8.2.0", "marked": "^5.1.1"}}