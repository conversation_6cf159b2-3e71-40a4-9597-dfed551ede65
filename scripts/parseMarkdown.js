const fs = require('fs');
const path = require('path');

/**
 * 解析《黑神话悟空妖怪平生录》markdown文件
 * 将其转换为结构化的JSON数据
 */

class MarkdownParser {
    constructor() {
        this.monsters = [];
        this.currentChapter = '';
        this.chapterMap = {
            '一、小妖': 'xiaoyao',
            '二、头目': 'toumu',
            '三、妖王': 'yaowang',
            '四、人物': 'renwu'
        };
    }

    /**
     * 解析markdown文件
     */
    parseMarkdown() {
        try {
            const markdownPath = path.join(__dirname, '../黑神话悟空妖怪平生录.md');
            const content = fs.readFileSync(markdownPath, 'utf-8');
            
            // 按行分割内容
            const lines = content.split('\n');
            
            let currentMonster = null;
            let isInPoetry = false;
            let isInStory = false;
            let poetryLines = [];
            let storyLines = [];
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                
                // 检测章节标题
                if (line.startsWith('# ')) {
                    this.currentChapter = line.substring(2).trim();
                    continue;
                }
                
                // 检测妖怪标题 (## 1.1 狼斥候)
                if (line.match(/^## \d+\.\d+\s+(.+)$/)) {
                    // 保存上一个妖怪
                    if (currentMonster) {
                        currentMonster.story = storyLines.join('\n').trim();
                        this.monsters.push(currentMonster);
                    }
                    
                    // 开始新妖怪
                    const matches = line.match(/^## (\d+)\.(\d+)\s+(.+)$/);
                    const chapterNum = matches[1];
                    const monsterNum = matches[2];
                    const name = matches[3];
                    
                    currentMonster = {
                        id: `${chapterNum}_${monsterNum}`,
                        chapter: this.currentChapter,
                        chapterCode: this.chapterMap[this.currentChapter] || 'unknown',
                        chapterNum: parseInt(chapterNum),
                        monsterNum: parseInt(monsterNum),
                        name: name,
                        poetry: '',
                        story: '',
                        image: ''
                    };
                    
                    isInPoetry = false;
                    isInStory = false;
                    poetryLines = [];
                    storyLines = [];
                    continue;
                }
                
                // 检测诗词开始 (> 开头)
                if (line.startsWith('> ') && currentMonster) {
                    if (!isInPoetry) {
                        isInPoetry = true;
                        isInStory = false;
                    }
                    poetryLines.push(line.substring(2));
                    continue;
                }
                
                // 检测诗词结束和故事开始
                if (isInPoetry && line === '' && currentMonster) {
                    currentMonster.poetry = poetryLines.join('\n');
                    isInPoetry = false;
                    continue;
                }
                
                // 检测图片
                if (line.match(/^!\[.*\]\(.*\)$/) && currentMonster) {
                    const imageMatch = line.match(/^!\[.*\]\((.+)\)$/);
                    if (imageMatch) {
                        currentMonster.image = imageMatch[1];
                    }
                    continue;
                }
                
                // 收集故事内容
                if (currentMonster && !isInPoetry && line !== '' && !line.startsWith('#') && !line.match(/^!\[.*\]\(.*\)$/)) {
                    isInStory = true;
                    storyLines.push(line);
                }
            }
            
            // 保存最后一个妖怪
            if (currentMonster) {
                currentMonster.story = storyLines.join('\n').trim();
                this.monsters.push(currentMonster);
            }
            
            console.log(`解析完成，共解析出 ${this.monsters.length} 个妖怪`);
            return this.monsters;
            
        } catch (error) {
            console.error('解析markdown文件失败:', error);
            throw error;
        }
    }

    /**
     * 保存解析结果为JSON文件
     */
    saveToJson() {
        try {
            const outputPath = path.join(__dirname, '../data/monsters.json');
            
            // 确保data目录存在
            const dataDir = path.dirname(outputPath);
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }
            
            // 保存数据
            fs.writeFileSync(outputPath, JSON.stringify(this.monsters, null, 2), 'utf-8');
            console.log(`数据已保存到: ${outputPath}`);
            
            // 生成统计信息
            const stats = this.generateStats();
            const statsPath = path.join(__dirname, '../data/stats.json');
            fs.writeFileSync(statsPath, JSON.stringify(stats, null, 2), 'utf-8');
            console.log(`统计信息已保存到: ${statsPath}`);
            
        } catch (error) {
            console.error('保存JSON文件失败:', error);
            throw error;
        }
    }

    /**
     * 生成统计信息
     */
    generateStats() {
        const stats = {
            total: this.monsters.length,
            chapters: {}
        };
        
        this.monsters.forEach(monster => {
            if (!stats.chapters[monster.chapter]) {
                stats.chapters[monster.chapter] = {
                    name: monster.chapter,
                    code: monster.chapterCode,
                    count: 0,
                    monsters: []
                };
            }
            stats.chapters[monster.chapter].count++;
            stats.chapters[monster.chapter].monsters.push({
                id: monster.id,
                name: monster.name
            });
        });
        
        return stats;
    }
}

// 执行解析
if (require.main === module) {
    const parser = new MarkdownParser();
    parser.parseMarkdown();
    parser.saveToJson();
}

module.exports = MarkdownParser;
