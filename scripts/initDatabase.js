const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');

/**
 * 初始化SQLite数据库
 * 创建表结构并导入妖怪数据
 */

class DatabaseInitializer {
    constructor() {
        this.dbPath = path.join(__dirname, '../database/monsters.db');
        this.db = null;
    }

    /**
     * 初始化数据库
     */
    async init() {
        try {
            // 确保database目录存在
            const dbDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dbDir)) {
                fs.mkdirSync(dbDir, { recursive: true });
            }

            // 连接数据库
            this.db = new sqlite3.Database(this.dbPath);
            console.log('数据库连接成功');

            // 创建表结构
            await this.createTables();
            
            // 导入数据
            await this.importData();
            
            console.log('数据库初始化完成');
            
        } catch (error) {
            console.error('数据库初始化失败:', error);
            throw error;
        } finally {
            if (this.db) {
                this.db.close();
            }
        }
    }

    /**
     * 创建表结构
     */
    createTables() {
        return new Promise((resolve, reject) => {
            const sql = `
                -- 妖怪表
                CREATE TABLE IF NOT EXISTS monsters (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    chapter TEXT NOT NULL,
                    chapter_code TEXT NOT NULL,
                    chapter_num INTEGER NOT NULL,
                    monster_num INTEGER NOT NULL,
                    poetry TEXT,
                    story TEXT,
                    image TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );

                -- 章节表
                CREATE TABLE IF NOT EXISTS chapters (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    code TEXT NOT NULL UNIQUE,
                    chapter_num INTEGER NOT NULL,
                    description TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );

                -- 创建索引
                CREATE INDEX IF NOT EXISTS idx_monsters_chapter ON monsters(chapter_code);
                CREATE INDEX IF NOT EXISTS idx_monsters_name ON monsters(name);
                CREATE INDEX IF NOT EXISTS idx_monsters_chapter_num ON monsters(chapter_num, monster_num);

                -- 搜索优化索引
                CREATE INDEX IF NOT EXISTS idx_monsters_name_search ON monsters(name COLLATE NOCASE);
                CREATE INDEX IF NOT EXISTS idx_monsters_poetry_search ON monsters(poetry);
                CREATE INDEX IF NOT EXISTS idx_monsters_story_search ON monsters(story);
            `;

            this.db.exec(sql, (err) => {
                if (err) {
                    reject(err);
                } else {
                    console.log('数据表创建成功');
                    resolve();
                }
            });
        });
    }

    /**
     * 导入妖怪数据
     */
    async importData() {
        try {
            // 读取解析后的JSON数据
            const dataPath = path.join(__dirname, '../data/monsters.json');
            if (!fs.existsSync(dataPath)) {
                throw new Error('妖怪数据文件不存在，请先运行 npm run parse-data');
            }

            const monsters = JSON.parse(fs.readFileSync(dataPath, 'utf-8'));
            console.log(`准备导入 ${monsters.length} 个妖怪数据`);

            // 先插入章节数据
            await this.insertChapters(monsters);
            
            // 再插入妖怪数据
            await this.insertMonsters(monsters);
            
        } catch (error) {
            console.error('导入数据失败:', error);
            throw error;
        }
    }

    /**
     * 插入章节数据
     */
    insertChapters(monsters) {
        return new Promise((resolve, reject) => {
            // 提取唯一的章节
            const chapters = [];
            const chapterSet = new Set();

            monsters.forEach(monster => {
                if (!chapterSet.has(monster.chapter)) {
                    chapterSet.add(monster.chapter);
                    chapters.push({
                        name: monster.chapter,
                        code: monster.chapterCode,
                        chapter_num: monster.chapterNum,
                        description: this.getChapterDescription(monster.chapter)
                    });
                }
            });

            // 清空现有章节数据
            this.db.run('DELETE FROM chapters', (err) => {
                if (err) {
                    reject(err);
                    return;
                }

                if (chapters.length === 0) {
                    console.log('没有章节数据需要插入');
                    resolve();
                    return;
                }

                // 插入章节数据
                const stmt = this.db.prepare(`
                    INSERT INTO chapters (name, code, chapter_num, description)
                    VALUES (?, ?, ?, ?)
                `);

                let completed = 0;
                let hasError = false;

                chapters.forEach(chapter => {
                    stmt.run([chapter.name, chapter.code, chapter.chapter_num, chapter.description], (err) => {
                        if (err && !hasError) {
                            hasError = true;
                            stmt.finalize();
                            reject(err);
                            return;
                        }
                        completed++;
                        if (completed === chapters.length && !hasError) {
                            stmt.finalize();
                            console.log(`章节数据插入完成，共 ${chapters.length} 个章节`);
                            resolve();
                        }
                    });
                });
            });
        });
    }

    /**
     * 插入妖怪数据
     */
    insertMonsters(monsters) {
        return new Promise((resolve, reject) => {
            // 清空现有妖怪数据
            this.db.run('DELETE FROM monsters', (err) => {
                if (err) {
                    reject(err);
                    return;
                }

                if (monsters.length === 0) {
                    console.log('没有妖怪数据需要插入');
                    resolve();
                    return;
                }

                // 插入妖怪数据
                const stmt = this.db.prepare(`
                    INSERT INTO monsters (id, name, chapter, chapter_code, chapter_num, monster_num, poetry, story, image)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                `);

                let completed = 0;
                let hasError = false;

                monsters.forEach(monster => {
                    stmt.run([
                        monster.id,
                        monster.name,
                        monster.chapter,
                        monster.chapterCode,
                        monster.chapterNum,
                        monster.monsterNum,
                        monster.poetry,
                        monster.story,
                        monster.image
                    ], (err) => {
                        if (err && !hasError) {
                            hasError = true;
                            stmt.finalize();
                            reject(err);
                            return;
                        }
                        completed++;
                        if (completed === monsters.length && !hasError) {
                            stmt.finalize();
                            console.log(`妖怪数据插入完成，共 ${monsters.length} 个妖怪`);
                            resolve();
                        }
                    });
                });
            });
        });
    }

    /**
     * 获取章节描述
     */
    getChapterDescription(chapterName) {
        const descriptions = {
            '一、小妖': '初入江湖的小妖们，虽然实力微弱，但各有各的故事和际遇',
            '二、头目': '各路头目精英，拥有一定实力和地位的妖怪们',
            '三、妖王': '称霸一方的妖王们，拥有强大的力量和复杂的过往',
            '四、人物': '西游路上的重要人物，或敌或友，各有传奇'
        };
        return descriptions[chapterName] || '';
    }
}

// 执行初始化
if (require.main === module) {
    const initializer = new DatabaseInitializer();
    initializer.init().catch(console.error);
}

module.exports = DatabaseInitializer;
