const express = require('express');
const cors = require('cors');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

/**
 * 黑神话悟空妖怪平生录 - 后端服务
 * 提供妖怪数据的API接口
 */

class MonsterServer {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 3000;
        this.dbPath = path.join(__dirname, '../database/monsters.db');
        this.db = null;
        
        this.initMiddleware();
        this.initDatabase();
        this.initRoutes();
    }

    /**
     * 初始化中间件
     */
    initMiddleware() {
        // 启用CORS
        this.app.use(cors());
        
        // 解析JSON请求体
        this.app.use(express.json());
        
        // 静态文件服务 - 提供图片访问
        this.app.use('/images', express.static(path.join(__dirname, '..')));
        
        // 静态文件服务 - 前端构建文件
        this.app.use(express.static(path.join(__dirname, '../client/dist')));
        
        // 请求日志
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
            next();
        });
    }

    /**
     * 初始化数据库连接
     */
    initDatabase() {
        this.db = new sqlite3.Database(this.dbPath, (err) => {
            if (err) {
                console.error('数据库连接失败:', err);
            } else {
                console.log('数据库连接成功');
            }
        });
    }

    /**
     * 初始化路由
     */
    initRoutes() {
        // API路由前缀
        const apiRouter = express.Router();
        
        // 获取所有章节
        apiRouter.get('/chapters', this.getChapters.bind(this));
        
        // 获取妖怪列表（支持分页和筛选）
        apiRouter.get('/monsters', this.getMonsters.bind(this));
        
        // 获取妖怪详情
        apiRouter.get('/monsters/:id', this.getMonsterById.bind(this));
        
        // 搜索妖怪
        apiRouter.get('/search', this.searchMonsters.bind(this));
        
        // 获取统计信息
        apiRouter.get('/stats', this.getStats.bind(this));
        
        // 随机获取一个妖怪
        apiRouter.get('/random', this.getRandomMonster.bind(this));
        
        this.app.use('/api', apiRouter);
        
        // 前端路由 - SPA支持
        this.app.get('*', (req, res) => {
            res.sendFile(path.join(__dirname, '../client/dist/index.html'));
        });
    }

    /**
     * 获取所有章节
     */
    getChapters(req, res) {
        const sql = 'SELECT * FROM chapters ORDER BY chapter_num';
        
        this.db.all(sql, [], (err, rows) => {
            if (err) {
                console.error('获取章节失败:', err);
                res.status(500).json({ error: '获取章节失败' });
                return;
            }
            
            res.json({
                success: true,
                data: rows
            });
        });
    }

    /**
     * 获取妖怪列表
     */
    getMonsters(req, res) {
        const { 
            page = 1, 
            pageSize = 20, 
            chapter, 
            name,
            sortBy = 'chapter_num,monster_num',
            sortOrder = 'ASC'
        } = req.query;
        
        let sql = 'SELECT * FROM monsters WHERE 1=1';
        let countSql = 'SELECT COUNT(*) as total FROM monsters WHERE 1=1';
        const params = [];
        
        // 章节筛选
        if (chapter) {
            sql += ' AND chapter_code = ?';
            countSql += ' AND chapter_code = ?';
            params.push(chapter);
        }
        
        // 名称搜索
        if (name) {
            sql += ' AND name LIKE ?';
            countSql += ' AND name LIKE ?';
            params.push(`%${name}%`);
        }
        
        // 排序
        const validSortFields = ['chapter_num', 'monster_num', 'name', 'created_at'];
        const sortFields = sortBy.split(',').filter(field => validSortFields.includes(field));
        if (sortFields.length > 0) {
            sql += ` ORDER BY ${sortFields.join(', ')} ${sortOrder}`;
        }
        
        // 分页
        const offset = (page - 1) * pageSize;
        sql += ' LIMIT ? OFFSET ?';
        params.push(parseInt(pageSize), offset);
        
        // 获取总数
        this.db.get(countSql, params.slice(0, -2), (err, countRow) => {
            if (err) {
                console.error('获取妖怪总数失败:', err);
                res.status(500).json({ error: '获取妖怪总数失败' });
                return;
            }
            
            // 获取数据
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    console.error('获取妖怪列表失败:', err);
                    res.status(500).json({ error: '获取妖怪列表失败' });
                    return;
                }
                
                res.json({
                    success: true,
                    data: {
                        monsters: rows,
                        pagination: {
                            page: parseInt(page),
                            pageSize: parseInt(pageSize),
                            total: countRow.total,
                            totalPages: Math.ceil(countRow.total / pageSize)
                        }
                    }
                });
            });
        });
    }

    /**
     * 获取妖怪详情
     */
    getMonsterById(req, res) {
        const { id } = req.params;
        const sql = 'SELECT * FROM monsters WHERE id = ?';
        
        this.db.get(sql, [id], (err, row) => {
            if (err) {
                console.error('获取妖怪详情失败:', err);
                res.status(500).json({ error: '获取妖怪详情失败' });
                return;
            }
            
            if (!row) {
                res.status(404).json({ error: '妖怪不存在' });
                return;
            }
            
            res.json({
                success: true,
                data: row
            });
        });
    }

    /**
     * 搜索妖怪 - 高性能版本
     */
    searchMonsters(req, res) {
        const { q, limit = 20 } = req.query;

        if (!q || q.trim().length === 0) {
            res.json({
                success: true,
                data: []
            });
            return;
        }

        const keyword = q.trim();
        const searchLimit = parseInt(limit);

        // 使用单个优化的SQL查询，通过CASE WHEN实现优先级排序
        const sql = `
            SELECT *,
                CASE
                    WHEN name = ? THEN 1
                    WHEN name LIKE ? THEN 2
                    WHEN name LIKE ? THEN 3
                    WHEN poetry LIKE ? THEN 4
                    WHEN story LIKE ? AND LENGTH(?) > 1 THEN 5
                    ELSE 6
                END as priority
            FROM monsters
            WHERE (
                name = ? OR
                name LIKE ? OR
                name LIKE ? OR
                poetry LIKE ? OR
                (story LIKE ? AND LENGTH(?) > 1)
            )
            ORDER BY priority, chapter_num, monster_num
            LIMIT ?
        `;

        const searchTerm = `%${keyword}%`;
        const startTerm = `${keyword}%`;

        const params = [
            // CASE WHEN 参数
            keyword,        // name = ?
            startTerm,      // name LIKE ? (开头匹配)
            searchTerm,     // name LIKE ? (包含匹配)
            searchTerm,     // poetry LIKE ?
            searchTerm,     // story LIKE ?
            keyword,        // LENGTH(?) > 1
            // WHERE 条件参数
            keyword,        // name = ?
            startTerm,      // name LIKE ?
            searchTerm,     // name LIKE ?
            searchTerm,     // poetry LIKE ?
            searchTerm,     // story LIKE ?
            keyword,        // LENGTH(?) > 1
            // LIMIT 参数
            searchLimit
        ];

        console.log(`搜索关键词: "${keyword}", 限制: ${searchLimit}`);
        const startTime = Date.now();

        this.db.all(sql, params, (err, rows) => {
            const endTime = Date.now();
            console.log(`搜索耗时: ${endTime - startTime}ms, 结果数量: ${rows ? rows.length : 0}`);

            if (err) {
                console.error('搜索妖怪失败:', err);
                res.status(500).json({ error: '搜索妖怪失败' });
                return;
            }

            // 移除priority字段
            const results = (rows || []).map(row => {
                const { priority, ...result } = row;
                return result;
            });

            res.json({
                success: true,
                data: results
            });
        });
    }

    /**
     * 获取统计信息
     */
    getStats(req, res) {
        const sql = `
            SELECT 
                chapter,
                chapter_code,
                chapter_num,
                COUNT(*) as count
            FROM monsters 
            GROUP BY chapter, chapter_code, chapter_num
            ORDER BY chapter_num
        `;
        
        this.db.all(sql, [], (err, rows) => {
            if (err) {
                console.error('获取统计信息失败:', err);
                res.status(500).json({ error: '获取统计信息失败' });
                return;
            }
            
            const totalSql = 'SELECT COUNT(*) as total FROM monsters';
            this.db.get(totalSql, [], (err, totalRow) => {
                if (err) {
                    console.error('获取总数失败:', err);
                    res.status(500).json({ error: '获取总数失败' });
                    return;
                }
                
                res.json({
                    success: true,
                    data: {
                        total: totalRow.total,
                        chapters: rows
                    }
                });
            });
        });
    }

    /**
     * 随机获取一个妖怪
     */
    getRandomMonster(req, res) {
        const sql = 'SELECT * FROM monsters ORDER BY RANDOM() LIMIT 1';
        
        this.db.get(sql, [], (err, row) => {
            if (err) {
                console.error('获取随机妖怪失败:', err);
                res.status(500).json({ error: '获取随机妖怪失败' });
                return;
            }
            
            res.json({
                success: true,
                data: row
            });
        });
    }

    /**
     * 启动服务器
     */
    start() {
        this.app.listen(this.port, () => {
            console.log(`服务器启动成功，端口: ${this.port}`);
            console.log(`访问地址: http://localhost:${this.port}`);
        });
    }

    /**
     * 关闭数据库连接
     */
    close() {
        if (this.db) {
            this.db.close((err) => {
                if (err) {
                    console.error('关闭数据库连接失败:', err);
                } else {
                    console.log('数据库连接已关闭');
                }
            });
        }
    }
}

// 启动服务器
const server = new MonsterServer();
server.start();

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');
    server.close();
    process.exit(0);
});

module.exports = MonsterServer;
