# 寻找天命人 - 黑神话悟空妖怪平生录

> 203个妖怪，203首小诗，203个妖生故事，带你去看妖怪的众生相

## 项目简介

本项目是基于《黑神话：悟空》游戏的妖怪平生录展示平台。通过现代化的Web技术栈，将原本的Markdown文档转换为交互式的数字化展示平台，让用户能够更好地浏览、搜索和收藏这些精彩的妖怪故事。

## 技术架构

### 前端技术栈
- **Vue.js 2.6** - 渐进式JavaScript框架
- **Element UI** - 基于Vue的组件库
- **Vue Router** - 前端路由管理
- **Vuex** - 状态管理
- **Axios** - HTTP客户端
- **Sass/SCSS** - CSS预处理器

### 后端技术栈
- **Node.js** - JavaScript运行环境
- **Express.js** - Web应用框架
- **SQLite** - 轻量级数据库
- **CORS** - 跨域资源共享

### 开发工具
- **Vue CLI** - Vue项目脚手架
- **Nodemon** - 自动重启开发服务器
- **Concurrently** - 并行运行多个命令

## 项目结构

```
project_demo/
├── client/                 # 前端项目
│   ├── public/            # 静态资源
│   ├── src/               # 源代码
│   │   ├── api/           # API接口
│   │   ├── components/    # Vue组件
│   │   │   ├── common/    # 通用组件
│   │   │   └── layout/    # 布局组件
│   │   ├── router/        # 路由配置
│   │   ├── store/         # Vuex状态管理
│   │   ├── styles/        # 样式文件
│   │   ├── views/         # 页面组件
│   │   ├── App.vue        # 根组件
│   │   └── main.js        # 入口文件
│   ├── package.json       # 前端依赖
│   └── vue.config.js      # Vue配置
├── server/                # 后端项目
│   └── app.js             # 服务器入口
├── scripts/               # 脚本文件
│   ├── parseMarkdown.js   # Markdown解析脚本
│   └── initDatabase.js    # 数据库初始化脚本
├── database/              # 数据库文件
├── data/                  # 解析后的JSON数据
├── 黑神话悟空妖怪平生录/    # 原始资源文件
├── package.json           # 项目依赖
└── README.md             # 项目说明
```

## 核心功能

### 1. 数据解析与存储
- **Markdown解析**: 自动解析原始Markdown文件，提取妖怪信息
- **结构化存储**: 将解析后的数据存储到SQLite数据库
- **图片资源管理**: 统一管理妖怪图片资源

### 2. 妖怪展示系统
- **妖怪列表**: 支持分页、筛选、排序的妖怪列表
- **妖怪详情**: 完整展示妖怪的诗词、故事和图片
- **章节浏览**: 按章节分类浏览妖怪
- **随机推荐**: 随机展示妖怪，增加探索乐趣

### 3. 搜索与筛选
- **全文搜索**: 支持按名称、诗词、故事内容搜索
- **智能建议**: 基于搜索历史的智能建议
- **高级筛选**: 按章节、类型等条件筛选

### 4. 用户交互功能
- **收藏系统**: 收藏喜欢的妖怪
- **搜索历史**: 记录用户搜索历史
- **响应式设计**: 适配各种设备屏幕

## 设计理念

### 1. 主题风格
- **暗色调设计**: 采用黑金配色，营造神秘氛围
- **古风元素**: 融入中国传统文化元素
- **现代交互**: 现代化的用户界面和交互体验

### 2. 用户体验
- **直观导航**: 清晰的导航结构和面包屑
- **快速加载**: 优化资源加载和缓存策略
- **移动优先**: 响应式设计，移动端体验优化

### 3. 数据组织
- **层次结构**: 章节 -> 妖怪 -> 详情的清晰层次
- **关联性**: 相关妖怪推荐和章节导航
- **可扩展性**: 支持后续功能扩展

## 技术亮点

### 1. 数据处理
```javascript
// Markdown解析核心逻辑
class MarkdownParser {
    parseMarkdown() {
        // 正则表达式匹配妖怪标题
        const titleRegex = /^## (\d+)\.(\d+)\s+(.+)$/

        // 解析诗词（> 开头的行）
        const poetryRegex = /^> (.+)$/

        // 提取图片链接
        const imageRegex = /^!\[.*\]\((.+)\)$/
    }
}
```

### 2. 状态管理
```javascript
// Vuex状态管理
const store = new Vuex.Store({
    state: {
        chapters: [],
        favorites: [],
        searchHistory: []
    },
    getters: {
        isFavorite: state => monsterId =>
            state.favorites.includes(monsterId)
    }
})
```

### 3. API设计
```javascript
// RESTful API设计
GET /api/monsters          // 获取妖怪列表
GET /api/monsters/:id      // 获取妖怪详情
GET /api/search           // 搜索妖怪
GET /api/chapters         // 获取章节列表
GET /api/stats           // 获取统计信息
```

## 业务逻辑流程

### 数据处理流程
1. **Markdown解析**: 读取原始markdown文件，使用正则表达式提取妖怪信息
2. **数据结构化**: 将解析结果转换为JSON格式，包含ID、名称、诗词、故事、图片等字段
3. **数据库存储**: 将JSON数据导入SQLite数据库，建立索引优化查询性能
4. **API服务**: Express服务器提供RESTful API接口
5. **前端展示**: Vue应用通过API获取数据并渲染页面

### 用户交互流程
1. **首页访问**: 展示项目介绍、统计数据、章节导航和精选妖怪
2. **浏览功能**: 用户可以按章节浏览、查看列表、搜索妖怪
3. **详情查看**: 点击妖怪卡片进入详情页，查看完整的诗词和故事
4. **交互操作**: 收藏妖怪、记录搜索历史、随机推荐等功能
5. **响应式体验**: 适配不同设备，提供流畅的用户体验

## 安装与运行

### 环境要求
- Node.js >= 14.0.0
- npm >= 6.0.0

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd project_demo
```

2. **安装依赖**
```bash
# 安装后端依赖
npm install

# 安装前端依赖
cd client
npm install
cd ..
```

3. **初始化数据**
```bash
# 解析Markdown文件并初始化数据库
npm run setup
```

4. **启动项目**
```bash
# 同时启动前后端服务
npm run dev
```

5. **访问应用**
- 前端地址: http://localhost:8080
- 后端API: http://localhost:3000

### 单独运行

```bash
# 只启动后端
npm run server

# 只启动前端
npm run client

# 构建生产版本
npm run build
```

## 部署说明

### 生产环境部署

1. **构建前端**
```bash
cd client
npm run build
```

2. **配置服务器**
```bash
# 设置环境变量
export NODE_ENV=production
export PORT=3000

# 启动服务
npm start
```

3. **Nginx配置**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /images/ {
        alias /path/to/project/黑神话悟空妖怪平生录/;
    }
}
```

## 性能优化

### 前端优化
- **代码分割**: 路由级别的代码分割
- **图片懒加载**: 妖怪图片懒加载
- **缓存策略**: 合理的缓存策略
- **压缩优化**: Gzip压缩和资源压缩

### 后端优化
- **数据库索引**: 关键字段建立索引
- **查询优化**: SQL查询优化
- **缓存机制**: 内存缓存热点数据
- **分页处理**: 大数据量分页处理

## 扩展功能

### 已规划功能
- [ ] 用户系统和个人中心
- [ ] 评论和评分系统
- [ ] 妖怪对比功能
- [ ] 数据可视化分析
- [ ] 移动端App
- [ ] 多语言支持

### 技术扩展
- [ ] 全文搜索引擎(Elasticsearch)
- [ ] 图片CDN加速
- [ ] 服务端渲染(SSR)
- [ ] 微服务架构
- [ ] 容器化部署

## 贡献指南

欢迎提交Issue和Pull Request来改进项目！

### 开发规范
- 遵循ESLint代码规范
- 提交前运行测试
- 编写清晰的提交信息
- 更新相关文档

## 许可证

本项目基于MIT许可证开源，详见LICENSE文件。

## 致谢

- 感谢《黑神话：悟空》游戏团队的精彩作品
- 感谢开源社区提供的优秀工具和库
- 感谢所有贡献者的努力

---

**寻找天命人项目组**
*让每个妖怪的故事都被听见*
